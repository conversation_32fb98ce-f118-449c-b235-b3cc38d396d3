# 💇‍♀️ Sistema de Agendamento para Salão de Beleza

Sistema completo de agendamento online para salões de beleza com interface moderna, gestão administrativa e integrações essenciais.

## 🚀 Tecnologias

### Frontend
- **Vue.js 3** - Framework progressivo
- **Pinia** - Gerenciamento de estado
- **Vue Router** - Roteamento
- **Tailwind CSS** - Estilização
- **Vite** - Build tool

### Backend
- **FastAPI** - Framework web moderno
- **SQLAlchemy** - ORM
- **Alembic** - Migrações de banco
- **Pydantic** - Validação de dados
- **JWT** - Autenticação

### Banco de Dados
- **PostgreSQL** - Banco principal
- **Redis** - Cache e sessões

### Infraestrutura
- **Docker** - Containerização
- **Docker Compose** - Orquestração
- **Nginx** - Proxy reverso

### Integrações
- **Mercado Pago** - Pagamentos
- **Stripe** - Pagamentos internacionais
- **PIX** - Pagamentos instantâneos
- **SendGrid** - E-mail
- **Twilio** - SMS
- **WhatsApp Business API** - WhatsApp
- **Google Calendar API** - Sincronização

## 📁 Estrutura do Projeto

```
salon/
├── backend/                 # API FastAPI
│   ├── app/
│   │   ├── api/            # Endpoints da API
│   │   ├── core/           # Configurações e segurança
│   │   ├── models/         # Modelos SQLAlchemy
│   │   ├── schemas/        # Schemas Pydantic
│   │   ├── services/       # Lógica de negócio
│   │   └── utils/          # Utilitários
│   ├── alembic/            # Migrações
│   ├── tests/              # Testes
│   └── requirements.txt
├── frontend/               # Aplicação Vue.js
│   ├── src/
│   │   ├── components/     # Componentes reutilizáveis
│   │   ├── views/          # Páginas
│   │   ├── stores/         # Pinia stores
│   │   ├── services/       # Serviços API
│   │   └── utils/          # Utilitários
│   ├── public/
│   └── package.json
├── docker-compose.yml      # Orquestração
├── .env.example           # Variáveis de ambiente
└── docs/                  # Documentação
```

## 🎯 Funcionalidades

### Para Clientes
- ✅ Agendamento online intuitivo
- ✅ Seleção de serviços e profissionais
- ✅ Visualização de disponibilidade em tempo real
- ✅ Histórico de agendamentos
- ✅ Notificações automáticas
- ✅ Pagamento online
- ✅ Cancelamento e reagendamento

### Para Administradores
- ✅ Dashboard de gestão
- ✅ Agenda diária/semanal
- ✅ Gerenciamento de profissionais
- ✅ Cadastro de serviços e preços
- ✅ Relatórios de faturamento
- ✅ Configurações personalizáveis

### Recursos Avançados
- ✅ Sistema de fidelidade
- ✅ Lista de espera
- ✅ API para integrações
- ✅ Dark mode
- ✅ Acessibilidade (WCAG)

## 🔧 Instalação e Execução

### Pré-requisitos
- Docker e Docker Compose
- Node.js 18+ (para desenvolvimento frontend)
- Python 3.11+ (para desenvolvimento backend)

### Execução com Docker
```bash
# Clonar o repositório
git clone <repository-url>
cd salon

# Configurar variáveis de ambiente
cp .env.example .env

# Executar com Docker Compose
docker-compose up -d

# Acessar aplicação
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# Docs API: http://localhost:8000/docs
```

### Desenvolvimento Local

#### Backend
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou venv\Scripts\activate  # Windows
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### Frontend
```bash
cd frontend
npm install
npm run dev
```

## 📊 Banco de Dados

### Principais Entidades
- **Users** - Clientes e funcionários
- **Services** - Serviços oferecidos
- **Professionals** - Profissionais do salão
- **Appointments** - Agendamentos
- **Payments** - Pagamentos
- **Notifications** - Notificações enviadas

## 🔐 Segurança

- Autenticação JWT com refresh tokens
- Criptografia de senhas com bcrypt
- Validação de dados com Pydantic
- Rate limiting
- CORS configurado
- Logs de auditoria

## 📱 API Endpoints

### Autenticação
- `POST /auth/login` - Login
- `POST /auth/register` - Registro
- `POST /auth/refresh` - Refresh token
- `POST /auth/logout` - Logout

### Agendamentos
- `GET /appointments` - Listar agendamentos
- `POST /appointments` - Criar agendamento
- `PUT /appointments/{id}` - Atualizar agendamento
- `DELETE /appointments/{id}` - Cancelar agendamento

### Serviços
- `GET /services` - Listar serviços
- `POST /services` - Criar serviço (admin)
- `PUT /services/{id}` - Atualizar serviço (admin)

### Profissionais
- `GET /professionals` - Listar profissionais
- `GET /professionals/{id}/availability` - Disponibilidade

## 🧪 Testes

```bash
# Backend
cd backend
pytest

# Frontend
cd frontend
npm run test
```

## 📈 Deploy

### Produção com Docker
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Variáveis de Ambiente Necessárias
- `DATABASE_URL` - URL do PostgreSQL
- `REDIS_URL` - URL do Redis
- `SECRET_KEY` - Chave secreta JWT
- `MERCADOPAGO_ACCESS_TOKEN` - Token Mercado Pago
- `STRIPE_SECRET_KEY` - Chave Stripe
- `SENDGRID_API_KEY` - Chave SendGrid
- `TWILIO_ACCOUNT_SID` - SID Twilio
- `WHATSAPP_TOKEN` - Token WhatsApp Business

## 📄 Licença

MIT License

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📞 Suporte

Para dúvidas e suporte, entre em contato através dos issues do GitHub.
