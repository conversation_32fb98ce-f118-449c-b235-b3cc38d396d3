# Domain Configuration
DOMAIN=localhost
# For production, use your actual domain: DOMAIN=yoursalon.com

# Database Configuration
DATABASE_URL=****************************************************/salon_db
POSTGRES_USER=salon_user
POSTGRES_PASSWORD=salon_password
POSTGRES_DB=salon_db

# Redis Configuration
REDIS_PASSWORD=redis_password
REDIS_URL=redis://:redis_password@redis:6379/0

# Traefik Labels Configuration (usando Traefik externo)
# Certifique-se de que seu Traefik externo tem a network 'web' configurada
FLOWER_AUTH=admin:$2y$10$K8V2VzWzVzWzVzWzVzWzVe   # admin:admin (change in production)
ADMINER_AUTH=admin:$2y$10$K8V2VzWzVzWzVzWzVzWzVe  # admin:admin (change in production)

# Production Security
FLOWER_USER=admin
FLOWER_PASSWORD=change-this-password
ADMIN_IPS=127.0.0.1/32,10.0.0.0/8  # IPs allowed to access admin tools

# JWT Configuration
SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME="Salon Booking System"
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Email Configuration (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME="Your Salon Name"

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# WhatsApp Configuration
WHATSAPP_TOKEN=your-whatsapp-business-token
WHATSAPP_PHONE_NUMBER_ID=your-phone-number-id

# Payment Configuration
# Mercado Pago
MERCADOPAGO_ACCESS_TOKEN=your-mercadopago-access-token
MERCADOPAGO_PUBLIC_KEY=your-mercadopago-public-key

# Stripe
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# PIX Configuration
PIX_KEY=your-pix-key
PIX_BANK_CODE=your-bank-code

# Google Calendar Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback

# File Upload Configuration
MAX_FILE_SIZE=5242880  # 5MB
UPLOAD_FOLDER=uploads/
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif

# Application Configuration
DEBUG=true
ENVIRONMENT=development
LOG_LEVEL=INFO

# Application Configuration
APP_NAME=Salon Booking

# Frontend Configuration
VITE_API_BASE_URL=https://api.localhost
VITE_APP_NAME="Salon Booking"
VITE_APP_ENV=development
VITE_MERCADOPAGO_PUBLIC_KEY=your-mercadopago-public-key
VITE_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_TIMEOUT_MINUTES=60
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Notification Settings
APPOINTMENT_REMINDER_HOURS=24
APPOINTMENT_CONFIRMATION_REQUIRED=true
SMS_NOTIFICATIONS_ENABLED=true
EMAIL_NOTIFICATIONS_ENABLED=true
WHATSAPP_NOTIFICATIONS_ENABLED=true

# Business Configuration
BUSINESS_NAME="Your Salon Name"
BUSINESS_ADDRESS="Your Salon Address"
BUSINESS_PHONE="+**********"
BUSINESS_EMAIL="<EMAIL>"
BUSINESS_TIMEZONE="America/Sao_Paulo"

# Working Hours (24h format)
BUSINESS_HOURS_START=09:00
BUSINESS_HOURS_END=18:00
BUSINESS_DAYS="1,2,3,4,5,6"  # Monday=1, Sunday=7

# Appointment Configuration
DEFAULT_APPOINTMENT_DURATION=60  # minutes
APPOINTMENT_BUFFER_TIME=15  # minutes between appointments
MAX_ADVANCE_BOOKING_DAYS=30
MIN_ADVANCE_BOOKING_HOURS=2

# Cache Configuration
CACHE_TTL_SECONDS=300
CACHE_MAX_SIZE=1000
