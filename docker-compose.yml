version: '3.8'

services:
  # Traefik Reverse Proxy
  traefik:
    image: traefik:v3.0
    container_name: salon_traefik
    restart: unless-stopped
    command:
      - --api.dashboard=true
      - --api.debug=true
      - --log.level=INFO
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --providers.docker.network=salon_network
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=${ACME_EMAIL:-<EMAIL>}
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
      - --certificatesresolvers.letsencrypt.acme.caserver=https://acme-staging-v02.api.letsencrypt.org/directory
      # Global redirect to https
      - --entrypoints.web.http.redirections.entrypoint.to=websecure
      - --entrypoints.web.http.redirections.entrypoint.scheme=https
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_letsencrypt:/letsencrypt
    networks:
      - salon_network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.${DOMAIN:-localhost}`)"
      - "traefik.http.routers.traefik.service=api@internal"
      - "traefik.http.routers.traefik.middlewares=auth"
      - "traefik.http.middlewares.auth.basicauth.users=${TRAEFIK_AUTH:-admin:$$2y$$10$$K8V2VzWzVzWzVzWzVzWzVe}"
      - "traefik.http.routers.traefik.tls.certresolver=letsencrypt"

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: salon_postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-salon_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-salon_password}
      POSTGRES_DB: ${POSTGRES_DB:-salon_db}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - salon_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-salon_user} -d ${POSTGRES_DB:-salon_db}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: salon_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    networks:
      - salon_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: salon_backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-salon_user}:${POSTGRES_PASSWORD:-salon_password}@postgres:5432/${POSTGRES_DB:-salon_db}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-super-secret-key-change-in-production}
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - DEBUG=${DEBUG:-true}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:3000,https://salon.${DOMAIN:-localhost}}
    env_file:
      - .env
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - salon_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`api.${DOMAIN:-localhost}`) || (Host(`${DOMAIN:-localhost}`) && PathPrefix(`/api`))"
      - "traefik.http.routers.backend.entrypoints=websecure"
      - "traefik.http.routers.backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.backend.loadbalancer.server.port=8000"
      - "traefik.http.middlewares.backend-cors.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.backend-cors.headers.accesscontrolallowheaders=*"
      - "traefik.http.middlewares.backend-cors.headers.accesscontrolalloworiginlist=https://${DOMAIN:-localhost},https://www.${DOMAIN:-localhost}"
      - "traefik.http.middlewares.backend-cors.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.backend-cors.headers.addvaryheader=true"
      - "traefik.http.routers.backend.middlewares=backend-cors"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: salon_frontend
    restart: unless-stopped
    environment:
      - VITE_API_BASE_URL=https://api.${DOMAIN:-localhost}
      - VITE_APP_NAME=${APP_NAME:-Salon Booking}
      - VITE_APP_ENV=${ENVIRONMENT:-development}
    env_file:
      - .env
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - salon_network
    command: npm run dev -- --host 0.0.0.0 --port 3000
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`${DOMAIN:-localhost}`) || Host(`www.${DOMAIN:-localhost}`)"
      - "traefik.http.routers.frontend.entrypoints=websecure"
      - "traefik.http.routers.frontend.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend.loadbalancer.server.port=3000"
      # Redirect www to non-www
      - "traefik.http.middlewares.www-redirect.redirectregex.regex=^https://www\\.(.+)"
      - "traefik.http.middlewares.www-redirect.redirectregex.replacement=https://$${1}"
      - "traefik.http.middlewares.www-redirect.redirectregex.permanent=true"
      - "traefik.http.routers.frontend.middlewares=www-redirect"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker (for background tasks)
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: salon_celery_worker
    restart: unless-stopped
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-salon_user}:${POSTGRES_PASSWORD:-salon_password}@postgres:5432/${POSTGRES_DB:-salon_db}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-super-secret-key-change-in-production}
      - ENVIRONMENT=${ENVIRONMENT:-development}
    env_file:
      - .env
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - salon_network

  # Celery Beat (for scheduled tasks)
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: salon_celery_beat
    restart: unless-stopped
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-salon_user}:${POSTGRES_PASSWORD:-salon_password}@postgres:5432/${POSTGRES_DB:-salon_db}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-super-secret-key-change-in-production}
      - ENVIRONMENT=${ENVIRONMENT:-development}
    env_file:
      - .env
    volumes:
      - ./backend:/app
      - celery_beat_data:/app/celerybeat-schedule
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - salon_network

  # Flower (Celery monitoring)
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: salon_flower
    restart: unless-stopped
    command: celery -A app.core.celery flower --port=5555
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-salon_user}:${POSTGRES_PASSWORD:-salon_password}@postgres:5432/${POSTGRES_DB:-salon_db}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-super-secret-key-change-in-production}
      - ENVIRONMENT=${ENVIRONMENT:-development}
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - salon_network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.flower.rule=Host(`flower.${DOMAIN:-localhost}`)"
      - "traefik.http.routers.flower.entrypoints=websecure"
      - "traefik.http.routers.flower.tls.certresolver=letsencrypt"
      - "traefik.http.services.flower.loadbalancer.server.port=5555"
      - "traefik.http.routers.flower.middlewares=flower-auth"
      - "traefik.http.middlewares.flower-auth.basicauth.users=${FLOWER_AUTH:-admin:$$2y$$10$$K8V2VzWzVzWzVzWzVzWzVe}"

  # Adminer (Database management)
  adminer:
    image: adminer:latest
    container_name: salon_adminer
    restart: unless-stopped
    depends_on:
      - postgres
    networks:
      - salon_network
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.adminer.rule=Host(`adminer.${DOMAIN:-localhost}`)"
      - "traefik.http.routers.adminer.entrypoints=websecure"
      - "traefik.http.routers.adminer.tls.certresolver=letsencrypt"
      - "traefik.http.services.adminer.loadbalancer.server.port=8080"
      - "traefik.http.routers.adminer.middlewares=adminer-auth"
      - "traefik.http.middlewares.adminer-auth.basicauth.users=${ADMINER_AUTH:-admin:$$2y$$10$$K8V2VzWzVzWzVzWzVzWzVe}"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local
  celery_beat_data:
    driver: local
  traefik_letsencrypt:
    driver: local

networks:
  salon_network:
    driver: bridge
