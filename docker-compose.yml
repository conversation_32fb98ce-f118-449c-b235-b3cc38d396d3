version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: salon_postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-salon_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-salon_password}
      POSTGRES_DB: ${POSTGRES_DB:-salon_db}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - salon_network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: salon_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - salon_network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: salon_backend
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-salon_user}:${POSTGRES_PASSWORD:-salon_password}@postgres:5432/${POSTGRES_DB:-salon_db}
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - salon_network
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: salon_frontend
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
    env_file:
      - .env
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - salon_network
    restart: unless-stopped
    command: npm run dev -- --host 0.0.0.0

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: salon_nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    networks:
      - salon_network
    restart: unless-stopped
    profiles:
      - production

  # PgAdmin (Development)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: salon_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - salon_network
    restart: unless-stopped
    profiles:
      - development

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local

networks:
  salon_network:
    driver: bridge
