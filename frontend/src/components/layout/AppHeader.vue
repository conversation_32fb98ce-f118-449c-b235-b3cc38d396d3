<template>
  <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
    <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center">
            <img
              class="h-8 w-auto"
              src="/logo.svg"
              alt="Salon Booking"
            />
            <span class="ml-2 text-xl font-bold text-gray-900 dark:text-white">
              Salon Booking
            </span>
          </router-link>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-4">
            <router-link
              to="/"
              class="nav-link"
              :class="{ 'nav-link-active': $route.name === 'Home' }"
            >
              Início
            </router-link>
            <router-link
              to="/services"
              class="nav-link"
              :class="{ 'nav-link-active': $route.name === 'Services' }"
            >
              Serviços
            </router-link>
            <router-link
              to="/professionals"
              class="nav-link"
              :class="{ 'nav-link-active': $route.name === 'Professionals' }"
            >
              Profissionais
            </router-link>
            <router-link
              to="/about"
              class="nav-link"
              :class="{ 'nav-link-active': $route.name === 'About' }"
            >
              Sobre
            </router-link>
            <router-link
              to="/contact"
              class="nav-link"
              :class="{ 'nav-link-active': $route.name === 'Contact' }"
            >
              Contato
            </router-link>
          </div>
        </div>

        <!-- Right side -->
        <div class="flex items-center space-x-4">
          <!-- Dark mode toggle -->
          <button
            @click="toggleDark"
            class="p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300 rounded-md"
          >
            <svg v-if="isDark" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd" />
            </svg>
            <svg v-else class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
            </svg>
          </button>

          <!-- User menu or auth buttons -->
          <div v-if="authStore.isAuthenticated" class="relative">
            <UserMenu />
          </div>
          <div v-else class="flex items-center space-x-2">
            <router-link
              to="/auth/login"
              class="btn btn-ghost btn-sm"
            >
              Entrar
            </router-link>
            <router-link
              to="/auth/register"
              class="btn btn-primary btn-sm"
            >
              Cadastrar
            </router-link>
          </div>

          <!-- Mobile menu button -->
          <button
            @click="toggleMobileMenu"
            class="md:hidden p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300 rounded-md"
          >
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div v-if="mobileMenu.show.value" class="md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200 dark:border-gray-700">
          <router-link
            to="/"
            class="mobile-nav-link"
            @click="mobileMenu.close()"
          >
            Início
          </router-link>
          <router-link
            to="/services"
            class="mobile-nav-link"
            @click="mobileMenu.close()"
          >
            Serviços
          </router-link>
          <router-link
            to="/professionals"
            class="mobile-nav-link"
            @click="mobileMenu.close()"
          >
            Profissionais
          </router-link>
          <router-link
            to="/about"
            class="mobile-nav-link"
            @click="mobileMenu.close()"
          >
            Sobre
          </router-link>
          <router-link
            to="/contact"
            class="mobile-nav-link"
            @click="mobileMenu.close()"
          >
            Contato
          </router-link>
          
          <!-- Mobile auth buttons -->
          <div v-if="!authStore.isAuthenticated" class="pt-4 border-t border-gray-200 dark:border-gray-700">
            <router-link
              to="/auth/login"
              class="mobile-nav-link"
              @click="mobileMenu.close()"
            >
              Entrar
            </router-link>
            <router-link
              to="/auth/register"
              class="mobile-nav-link"
              @click="mobileMenu.close()"
            >
              Cadastrar
            </router-link>
          </div>
        </div>
      </div>
    </nav>
  </header>
</template>

<script setup>
import { inject } from 'vue'
import { useDark, useToggle } from '@vueuse/core'
import { useAuthStore } from '@/stores/auth'
import UserMenu from '@/components/common/UserMenu.vue'

const authStore = useAuthStore()
const mobileMenu = inject('mobileMenu')

// Dark mode
const isDark = useDark()
const toggleDark = useToggle(isDark)

const toggleMobileMenu = () => {
  mobileMenu.toggle()
}
</script>

<style scoped>
.nav-link {
  @apply text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors;
}

.nav-link-active {
  @apply text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20;
}

.mobile-nav-link {
  @apply text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700 block px-3 py-2 rounded-md text-base font-medium transition-colors;
}
</style>
