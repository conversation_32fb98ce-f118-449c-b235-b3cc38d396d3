<template>
  <div class="relative">
    <!-- User button -->
    <button
      @click="showMenu = !showMenu"
      class="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
    >
      <img
        v-if="authStore.user?.avatar_url"
        :src="authStore.user.avatar_url"
        :alt="authStore.user.full_name"
        class="h-8 w-8 rounded-full object-cover"
      />
      <div
        v-else
        class="h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center"
      >
        <span class="text-white text-sm font-medium">
          {{ userInitials }}
        </span>
      </div>
      
      <div class="hidden md:block text-left">
        <p class="text-sm font-medium text-gray-900 dark:text-white">
          {{ authStore.user?.full_name }}
        </p>
        <p class="text-xs text-gray-500 dark:text-gray-400 capitalize">
          {{ authStore.user?.role }}
        </p>
      </div>
      
      <svg
        class="h-4 w-4 text-gray-400"
        :class="{ 'rotate-180': showMenu }"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>

    <!-- Dropdown menu -->
    <transition
      enter-active-class="transition ease-out duration-100"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <div
        v-if="showMenu"
        class="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"
      >
        <div class="py-1">
          <!-- User info (mobile) -->
          <div class="md:hidden px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              {{ authStore.user?.full_name }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              {{ authStore.user?.email }}
            </p>
          </div>

          <!-- Dashboard link -->
          <router-link
            :to="dashboardRoute"
            class="menu-item"
            @click="showMenu = false"
          >
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7z" />
            </svg>
            Dashboard
          </router-link>

          <!-- Profile -->
          <router-link
            :to="profileRoute"
            class="menu-item"
            @click="showMenu = false"
          >
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Meu Perfil
          </router-link>

          <!-- Appointments -->
          <router-link
            :to="appointmentsRoute"
            class="menu-item"
            @click="showMenu = false"
          >
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Agendamentos
          </router-link>

          <!-- Book appointment (clients only) -->
          <router-link
            v-if="authStore.isClient"
            to="/client/book"
            class="menu-item"
            @click="showMenu = false"
          >
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Agendar Serviço
          </router-link>

          <!-- Notifications -->
          <button
            @click="showNotifications = true; showMenu = false"
            class="menu-item w-full text-left"
          >
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-6H4v6zM16 3h5v5h-5V3zM4 3h6v6H4V3z" />
            </svg>
            Notificações
            <span
              v-if="unreadCount > 0"
              class="ml-auto bg-primary-500 text-white text-xs rounded-full px-2 py-0.5"
            >
              {{ unreadCount }}
            </span>
          </button>

          <div class="border-t border-gray-200 dark:border-gray-700"></div>

          <!-- Settings -->
          <button
            @click="showSettings = true; showMenu = false"
            class="menu-item w-full text-left"
          >
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Configurações
          </button>

          <!-- Logout -->
          <button
            @click="handleLogout"
            class="menu-item w-full text-left text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            Sair
          </button>
        </div>
      </div>
    </transition>

    <!-- Click outside to close -->
    <div
      v-if="showMenu"
      class="fixed inset-0 z-40"
      @click="showMenu = false"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { notificationsAPI } from '@/services/api'

const authStore = useAuthStore()

// State
const showMenu = ref(false)
const showNotifications = ref(false)
const showSettings = ref(false)
const unreadCount = ref(0)

// Computed
const userInitials = computed(() => {
  if (!authStore.user?.full_name) return 'U'
  return authStore.user.full_name
    .split(' ')
    .map(name => name.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
})

const dashboardRoute = computed(() => {
  const role = authStore.user?.role
  if (role === 'admin' || role === 'owner') {
    return '/admin'
  } else if (role === 'professional') {
    return '/professional'
  } else {
    return '/client'
  }
})

const profileRoute = computed(() => {
  const role = authStore.user?.role
  if (role === 'professional') {
    return '/professional/profile'
  } else {
    return '/client/profile'
  }
})

const appointmentsRoute = computed(() => {
  const role = authStore.user?.role
  if (role === 'admin' || role === 'owner') {
    return '/admin/appointments'
  } else if (role === 'professional') {
    return '/professional/appointments'
  } else {
    return '/client/appointments'
  }
})

// Methods
const handleLogout = async () => {
  showMenu.value = false
  await authStore.logout()
}

const fetchUnreadCount = async () => {
  try {
    const response = await notificationsAPI.getUnreadCount()
    unreadCount.value = response.data.unread_count
  } catch (error) {
    console.error('Error fetching unread count:', error)
  }
}

// Lifecycle
onMounted(() => {
  if (authStore.isAuthenticated) {
    fetchUnreadCount()
    
    // Poll for unread count every 30 seconds
    setInterval(fetchUnreadCount, 30000)
  }
})
</script>

<style scoped>
.menu-item {
  @apply flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}
</style>
