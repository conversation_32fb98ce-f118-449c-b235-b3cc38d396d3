@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
}

/* Custom component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-error {
    @apply btn bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }
  
  .btn-outline {
    @apply btn border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn border-transparent text-gray-700 bg-transparent hover:bg-gray-100 focus:ring-primary-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .btn-xl {
    @apply px-8 py-4 text-lg;
  }
  
  /* Card styles */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-750 rounded-b-lg;
  }
  
  /* Form styles */
  .form-group {
    @apply mb-4;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .form-select {
    @apply form-input pr-10 bg-white dark:bg-gray-700;
  }
  
  .form-textarea {
    @apply form-input resize-none;
  }
  
  .form-checkbox {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded;
  }
  
  .form-radio {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600;
  }
  
  .form-error {
    @apply mt-1 text-sm text-error-600 dark:text-error-400;
  }
  
  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800 dark:bg-primary-800 dark:text-primary-100;
  }
  
  .badge-secondary {
    @apply badge bg-secondary-100 text-secondary-800 dark:bg-secondary-800 dark:text-secondary-100;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800 dark:bg-warning-800 dark:text-warning-100;
  }
  
  .badge-error {
    @apply badge bg-error-100 text-error-800 dark:bg-error-800 dark:text-error-100;
  }
  
  /* Alert styles */
  .alert {
    @apply p-4 rounded-md border-l-4;
  }
  
  .alert-success {
    @apply alert bg-success-50 border-success-400 text-success-700 dark:bg-success-900 dark:text-success-100;
  }
  
  .alert-warning {
    @apply alert bg-warning-50 border-warning-400 text-warning-700 dark:bg-warning-900 dark:text-warning-100;
  }
  
  .alert-error {
    @apply alert bg-error-50 border-error-400 text-error-700 dark:bg-error-900 dark:text-error-100;
  }
  
  .alert-info {
    @apply alert bg-blue-50 border-blue-400 text-blue-700 dark:bg-blue-900 dark:text-blue-100;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* Skeleton loading */
  .skeleton {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
  }
  
  /* Divider */
  .divider {
    @apply border-t border-gray-200 dark:border-gray-700;
  }
  
  /* Avatar */
  .avatar {
    @apply inline-block rounded-full overflow-hidden bg-gray-100 dark:bg-gray-800;
  }
  
  .avatar-sm {
    @apply avatar h-8 w-8;
  }
  
  .avatar-md {
    @apply avatar h-10 w-10;
  }
  
  .avatar-lg {
    @apply avatar h-12 w-12;
  }
  
  .avatar-xl {
    @apply avatar h-16 w-16;
  }
  
  /* Status indicators */
  .status-dot {
    @apply inline-block w-2 h-2 rounded-full;
  }
  
  .status-online {
    @apply status-dot bg-success-400;
  }
  
  .status-offline {
    @apply status-dot bg-gray-400;
  }
  
  .status-busy {
    @apply status-dot bg-error-400;
  }
  
  .status-away {
    @apply status-dot bg-warning-400;
  }
}

/* Custom utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .glass {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.8);
  }
  
  .glass-dark {
    backdrop-filter: blur(10px);
    background-color: rgba(0, 0, 0, 0.8);
  }
}

/* Dark mode specific styles */
@media (prefers-color-scheme: dark) {
  .dark-auto {
    @apply dark;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}
