<template>
  <div class="min-h-screen">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-primary-600 to-primary-800 overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-20"></div>
      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
            Agende seus serviços de
            <span class="text-primary-200">beleza</span>
            online
          </h1>
          <p class="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
            Simplifique sua vida com nosso sistema de agendamento online. 
            Escolha seu profissional, horário e serviço favorito em poucos cliques.
          </p>
          
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <router-link
              to="/auth/register"
              class="btn btn-lg bg-white text-primary-600 hover:bg-gray-50 px-8 py-4 text-lg font-semibold rounded-lg shadow-lg transform hover:scale-105 transition-all"
            >
              Começar Agora
            </router-link>
            <router-link
              to="/services"
              class="btn btn-lg border-2 border-white text-white hover:bg-white hover:text-primary-600 px-8 py-4 text-lg font-semibold rounded-lg transition-all"
            >
              Ver Serviços
            </router-link>
          </div>
        </div>
      </div>
      
      <!-- Decorative elements -->
      <div class="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 bg-primary-400 rounded-full opacity-20"></div>
      <div class="absolute bottom-0 left-0 -mb-20 -ml-20 w-60 h-60 bg-primary-300 rounded-full opacity-20"></div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white dark:bg-gray-900">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Por que escolher nosso sistema?
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Oferecemos a melhor experiência em agendamento online para salões de beleza
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Feature 1 -->
          <div class="text-center p-6 rounded-lg hover:shadow-lg transition-shadow">
            <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Agendamento 24/7
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
              Agende seus serviços a qualquer hora do dia, mesmo fora do horário de funcionamento
            </p>
          </div>

          <!-- Feature 2 -->
          <div class="text-center p-6 rounded-lg hover:shadow-lg transition-shadow">
            <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Profissionais Qualificados
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
              Escolha entre nossos profissionais especializados e veja suas avaliações
            </p>
          </div>

          <!-- Feature 3 -->
          <div class="text-center p-6 rounded-lg hover:shadow-lg transition-shadow">
            <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-6H4v6zM16 3h5v5h-5V3zM4 3h6v6H4V3z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Notificações Inteligentes
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
              Receba lembretes por email, SMS ou WhatsApp sobre seus agendamentos
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Services Preview -->
    <section class="py-20 bg-gray-50 dark:bg-gray-800">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Nossos Serviços
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Oferecemos uma ampla gama de serviços de beleza para você se sentir ainda mais bonita
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Service cards will be loaded here -->
          <div
            v-for="service in featuredServices"
            :key="service.id"
            class="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
          >
            <img
              v-if="service.image_url"
              :src="service.image_url"
              :alt="service.name"
              class="w-full h-48 object-cover"
            />
            <div
              v-else
              class="w-full h-48 bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center"
            >
              <span class="text-white text-2xl font-bold">{{ service.name.charAt(0) }}</span>
            </div>
            
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                {{ service.name }}
              </h3>
              <p class="text-gray-600 dark:text-gray-400 mb-4">
                {{ service.description }}
              </p>
              
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <span class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {{ $filters.currency(service.current_price) }}
                  </span>
                  <span v-if="service.has_promotion" class="text-sm text-gray-500 line-through">
                    {{ $filters.currency(service.price) }}
                  </span>
                </div>
                
                <span class="text-sm text-gray-500 dark:text-gray-400">
                  {{ service.formatted_duration }}
                </span>
              </div>
              
              <router-link
                :to="`/services/${service.id}`"
                class="mt-4 w-full btn btn-primary"
              >
                Ver Detalhes
              </router-link>
            </div>
          </div>
        </div>

        <div class="text-center mt-12">
          <router-link
            to="/services"
            class="btn btn-outline btn-lg"
          >
            Ver Todos os Serviços
          </router-link>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-primary-600">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
          Pronto para se sentir ainda mais bonita?
        </h2>
        <p class="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
          Cadastre-se agora e agende seu primeiro serviço com desconto especial
        </p>
        
        <router-link
          to="/auth/register"
          class="btn btn-lg bg-white text-primary-600 hover:bg-gray-50 px-8 py-4 text-lg font-semibold rounded-lg shadow-lg transform hover:scale-105 transition-all"
        >
          Cadastrar Agora
        </router-link>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { servicesAPI } from '@/services/api'

// State
const featuredServices = ref([])
const loading = ref(false)

// Methods
const fetchFeaturedServices = async () => {
  try {
    loading.value = true
    const response = await servicesAPI.getFeatured(6)
    featuredServices.value = response.data
  } catch (error) {
    console.error('Error fetching featured services:', error)
    // Fallback to mock data
    featuredServices.value = [
      {
        id: '1',
        name: 'Corte Feminino',
        description: 'Corte moderno e personalizado para realçar sua beleza',
        current_price: 50.00,
        price: 50.00,
        has_promotion: false,
        formatted_duration: '1h',
        image_url: null
      },
      {
        id: '2',
        name: 'Coloração',
        description: 'Coloração profissional com produtos de alta qualidade',
        current_price: 80.00,
        price: 100.00,
        has_promotion: true,
        formatted_duration: '2h',
        image_url: null
      },
      {
        id: '3',
        name: 'Manicure',
        description: 'Cuidado completo para suas unhas',
        current_price: 25.00,
        price: 25.00,
        has_promotion: false,
        formatted_duration: '45min',
        image_url: null
      }
    ]
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchFeaturedServices()
})
</script>
