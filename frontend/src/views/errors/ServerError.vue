<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="text-center">
        <!-- 500 Icon -->
        <div class="mx-auto h-24 w-24 text-red-400 mb-6">
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        
        <!-- Error message -->
        <h1 class="text-6xl font-bold text-gray-900 dark:text-white mb-4">500</h1>
        <h2 class="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4">
          Erro interno do servidor
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-8">
          Algo deu errado em nossos servidores. Nossa equipe foi notificada e está trabalhando para resolver o problema.
        </p>
        
        <!-- Action buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            @click="refreshPage"
            class="btn btn-primary"
          >
            Tentar Novamente
          </button>
          <router-link
            to="/"
            class="btn btn-outline"
          >
            Voltar ao Início
          </router-link>
        </div>
        
        <!-- Help links -->
        <div class="mt-8 text-sm text-gray-500 dark:text-gray-400">
          <p>O problema persiste?</p>
          <div class="mt-2 space-x-4">
            <router-link to="/contact" class="text-primary-600 hover:text-primary-500">
              Reportar problema
            </router-link>
            <span>•</span>
            <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-500">
              Enviar email
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const refreshPage = () => {
  window.location.reload()
}
</script>
