<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="text-center">
        <!-- 404 Icon -->
        <div class="mx-auto h-24 w-24 text-primary-400 mb-6">
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.5-.935-6.072-2.456M15 21H3v-1.5A6.5 6.5 0 019.5 13h.5a6.5 6.5 0 016.5 6.5V21z" />
          </svg>
        </div>
        
        <!-- Error message -->
        <h1 class="text-6xl font-bold text-gray-900 dark:text-white mb-4">404</h1>
        <h2 class="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4">
          Página não encontrada
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-8">
          Desculpe, a página que você está procurando não existe ou foi movida.
        </p>
        
        <!-- Action buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link
            to="/"
            class="btn btn-primary"
          >
            Voltar ao Início
          </router-link>
          <button
            @click="$router.go(-1)"
            class="btn btn-outline"
          >
            Página Anterior
          </button>
        </div>
        
        <!-- Help links -->
        <div class="mt-8 text-sm text-gray-500 dark:text-gray-400">
          <p>Precisa de ajuda?</p>
          <div class="mt-2 space-x-4">
            <router-link to="/contact" class="text-primary-600 hover:text-primary-500">
              Entre em contato
            </router-link>
            <span>•</span>
            <router-link to="/services" class="text-primary-600 hover:text-primary-500">
              Ver serviços
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
