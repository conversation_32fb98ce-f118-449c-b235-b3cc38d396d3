<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="text-center">
        <!-- 403 Icon -->
        <div class="mx-auto h-24 w-24 text-red-400 mb-6">
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        </div>
        
        <!-- Error message -->
        <h1 class="text-6xl font-bold text-gray-900 dark:text-white mb-4">403</h1>
        <h2 class="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4">
          <PERSON><PERSON> negado
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-8">
          Você não tem permissão para acessar esta página. Entre em contato com o administrador se acredita que isso é um erro.
        </p>
        
        <!-- Action buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link
            to="/"
            class="btn btn-primary"
          >
            Voltar ao Início
          </router-link>
          <router-link
            v-if="!authStore.isAuthenticated"
            to="/auth/login"
            class="btn btn-outline"
          >
            Fazer Login
          </router-link>
          <button
            v-else
            @click="$router.go(-1)"
            class="btn btn-outline"
          >
            Página Anterior
          </button>
        </div>
        
        <!-- Help links -->
        <div class="mt-8 text-sm text-gray-500 dark:text-gray-400">
          <p>Precisa de ajuda?</p>
          <div class="mt-2 space-x-4">
            <router-link to="/contact" class="text-primary-600 hover:text-primary-500">
              Entre em contato
            </router-link>
            <span>•</span>
            <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-500">
              Enviar email
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
</script>
