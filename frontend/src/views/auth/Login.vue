<template>
  <form @submit.prevent="handleLogin" class="space-y-6">
    <!-- Email -->
    <div class="form-group">
      <label for="email" class="form-label">
        Email
      </label>
      <input
        id="email"
        v-model="form.email"
        type="email"
        required
        class="form-input"
        :class="{ 'border-red-500': errors.email }"
        placeholder="<EMAIL>"
      />
      <p v-if="errors.email" class="form-error">
        {{ errors.email }}
      </p>
    </div>

    <!-- Password -->
    <div class="form-group">
      <label for="password" class="form-label">
        Senha
      </label>
      <div class="relative">
        <input
          id="password"
          v-model="form.password"
          :type="showPassword ? 'text' : 'password'"
          required
          class="form-input pr-10"
          :class="{ 'border-red-500': errors.password }"
          placeholder="Sua senha"
        />
        <button
          type="button"
          @click="showPassword = !showPassword"
          class="absolute inset-y-0 right-0 pr-3 flex items-center"
        >
          <svg
            v-if="showPassword"
            class="h-5 w-5 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          <svg
            v-else
            class="h-5 w-5 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
          </svg>
        </button>
      </div>
      <p v-if="errors.password" class="form-error">
        {{ errors.password }}
      </p>
    </div>

    <!-- Remember me and forgot password -->
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <input
          id="remember"
          v-model="form.remember"
          type="checkbox"
          class="form-checkbox"
        />
        <label for="remember" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
          Lembrar de mim
        </label>
      </div>

      <router-link
        to="/auth/forgot-password"
        class="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
      >
        Esqueceu a senha?
      </router-link>
    </div>

    <!-- Submit button -->
    <button
      type="submit"
      :disabled="loading"
      class="w-full btn btn-primary"
      :class="{ 'opacity-50 cursor-not-allowed': loading }"
    >
      <span v-if="loading" class="flex items-center justify-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Entrando...
      </span>
      <span v-else>Entrar</span>
    </button>

    <!-- Error message -->
    <div v-if="errors.general" class="alert alert-error">
      {{ errors.general }}
    </div>
  </form>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// State
const loading = ref(false)
const showPassword = ref(false)

const form = reactive({
  email: '',
  password: '',
  remember: false
})

const errors = reactive({
  email: '',
  password: '',
  general: ''
})

// Methods
const clearErrors = () => {
  errors.email = ''
  errors.password = ''
  errors.general = ''
}

const validateForm = () => {
  clearErrors()
  let isValid = true

  if (!form.email) {
    errors.email = 'Email é obrigatório'
    isValid = false
  } else if (!/\S+@\S+\.\S+/.test(form.email)) {
    errors.email = 'Email inválido'
    isValid = false
  }

  if (!form.password) {
    errors.password = 'Senha é obrigatória'
    isValid = false
  } else if (form.password.length < 6) {
    errors.password = 'Senha deve ter pelo menos 6 caracteres'
    isValid = false
  }

  return isValid
}

const handleLogin = async () => {
  if (!validateForm()) return

  try {
    loading.value = true
    clearErrors()

    const result = await authStore.login({
      email: form.email,
      password: form.password
    })

    if (!result.success) {
      errors.general = result.error || 'Erro ao fazer login'
    }
  } catch (error) {
    console.error('Login error:', error)
    errors.general = 'Erro inesperado. Tente novamente.'
  } finally {
    loading.value = false
  }
}
</script>
