<template>
  <div class="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 dark:from-gray-900 dark:to-gray-800 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <!-- Logo -->
      <router-link to="/" class="flex justify-center">
        <img
          class="h-12 w-auto"
          src="/logo.svg"
          alt="Salon Booking"
        />
      </router-link>
      
      <!-- Page title -->
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
        {{ pageTitle }}
      </h2>
      
      <!-- Subtitle -->
      <p v-if="pageSubtitle" class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
        {{ pageSubtitle }}
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white dark:bg-gray-800 py-8 px-4 shadow-xl rounded-lg sm:px-10 border border-gray-200 dark:border-gray-700">
        <!-- Main content -->
        <router-view />
        
        <!-- Auth links -->
        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300 dark:border-gray-600" />
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
                Ou
              </span>
            </div>
          </div>

          <div class="mt-6 grid grid-cols-1 gap-3">
            <!-- Social login buttons -->
            <button
              type="button"
              class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
            >
              <svg class="h-5 w-5" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="currentColor"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="currentColor"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="currentColor"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              <span class="ml-2">Continuar com Google</span>
            </button>
            
            <button
              type="button"
              class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
            >
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
              <span class="ml-2">Continuar com Facebook</span>
            </button>
          </div>
        </div>
      </div>
      
      <!-- Footer links -->
      <div class="mt-6 text-center">
        <router-link
          v-if="$route.name === 'Login'"
          to="/auth/register"
          class="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
        >
          Não tem uma conta? Cadastre-se
        </router-link>
        
        <router-link
          v-else-if="$route.name === 'Register'"
          to="/auth/login"
          class="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
        >
          Já tem uma conta? Faça login
        </router-link>
        
        <router-link
          v-else
          to="/auth/login"
          class="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
        >
          Voltar ao login
        </router-link>
      </div>
      
      <!-- Back to home -->
      <div class="mt-4 text-center">
        <router-link
          to="/"
          class="text-sm text-gray-600 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300"
        >
          ← Voltar ao site
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const pageTitle = computed(() => {
  switch (route.name) {
    case 'Login':
      return 'Faça login na sua conta'
    case 'Register':
      return 'Crie sua conta'
    case 'ForgotPassword':
      return 'Esqueceu sua senha?'
    case 'ResetPassword':
      return 'Redefinir senha'
    case 'VerifyEmail':
      return 'Verificar email'
    default:
      return 'Autenticação'
  }
})

const pageSubtitle = computed(() => {
  switch (route.name) {
    case 'Login':
      return 'Acesse sua conta para gerenciar seus agendamentos'
    case 'Register':
      return 'Cadastre-se para agendar seus serviços de beleza'
    case 'ForgotPassword':
      return 'Digite seu email para receber as instruções de recuperação'
    case 'ResetPassword':
      return 'Digite sua nova senha'
    case 'VerifyEmail':
      return 'Verifique seu email para ativar sua conta'
    default:
      return null
  }
})
</script>
