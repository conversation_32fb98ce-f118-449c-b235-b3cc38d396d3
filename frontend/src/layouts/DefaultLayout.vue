<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Navigation -->
    <AppHeader />
    
    <!-- Main content -->
    <main class="flex-1">
      <router-view />
    </main>
    
    <!-- Footer -->
    <AppFooter />
    
    <!-- Mobile menu overlay -->
    <div
      v-if="showMobileMenu"
      class="fixed inset-0 z-40 lg:hidden"
      @click="showMobileMenu = false"
    >
      <div class="fixed inset-0 bg-black bg-opacity-25"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, provide } from 'vue'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppFooter from '@/components/layout/AppFooter.vue'

// Mobile menu state
const showMobileMenu = ref(false)

// Provide mobile menu state to child components
provide('mobileMenu', {
  show: showMobileMenu,
  toggle: () => {
    showMobileMenu.value = !showMobileMenu.value
  },
  close: () => {
    showMobileMenu.value = false
  }
})
</script>
