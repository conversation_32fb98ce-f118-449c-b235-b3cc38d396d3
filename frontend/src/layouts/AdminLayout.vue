<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Sidebar -->
    <AdminSidebar
      :show="showSidebar"
      @close="showSidebar = false"
    />
    
    <!-- Main content area -->
    <div class="lg:pl-64">
      <!-- Top navigation -->
      <div class="sticky top-0 z-10 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 lg:hidden">
        <div class="flex items-center justify-between px-4 py-2">
          <button
            @click="showSidebar = true"
            class="text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <span class="sr-only">Abrir menu</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          
          <h1 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ pageTitle }}
          </h1>
          
          <AdminUserMenu />
        </div>
      </div>
      
      <!-- Desktop header -->
      <div class="hidden lg:block bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                {{ pageTitle }}
              </h1>
              <p v-if="pageDescription" class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                {{ pageDescription }}
              </p>
            </div>
            
            <div class="flex items-center space-x-4">
              <!-- Notifications -->
              <AdminNotifications />
              
              <!-- User menu -->
              <AdminUserMenu />
            </div>
          </div>
        </div>
      </div>
      
      <!-- Page content -->
      <main class="flex-1">
        <div class="py-6">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumbs -->
            <AdminBreadcrumbs v-if="showBreadcrumbs" />
            
            <!-- Page content -->
            <router-view />
          </div>
        </div>
      </main>
    </div>
    
    <!-- Mobile sidebar overlay -->
    <div
      v-if="showSidebar"
      class="fixed inset-0 z-40 lg:hidden"
      @click="showSidebar = false"
    >
      <div class="fixed inset-0 bg-black bg-opacity-25"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, provide } from 'vue'
import { useRoute } from 'vue-router'
import AdminSidebar from '@/components/admin/AdminSidebar.vue'
import AdminUserMenu from '@/components/admin/AdminUserMenu.vue'
import AdminNotifications from '@/components/admin/AdminNotifications.vue'
import AdminBreadcrumbs from '@/components/admin/AdminBreadcrumbs.vue'

const route = useRoute()

// Sidebar state
const showSidebar = ref(false)

// Page info
const pageTitle = computed(() => {
  return route.meta.title || 'Dashboard'
})

const pageDescription = computed(() => {
  return route.meta.description || null
})

const showBreadcrumbs = computed(() => {
  return route.meta.showBreadcrumbs !== false
})

// Provide sidebar state to child components
provide('adminSidebar', {
  show: showSidebar,
  toggle: () => {
    showSidebar.value = !showSidebar.value
  },
  close: () => {
    showSidebar.value = false
  }
})
</script>
