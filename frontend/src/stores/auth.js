import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import api from '@/services/api'

export const useAuthStore = defineStore('auth', () => {
  const router = useRouter()
  const toast = useToast()
  
  // State
  const user = ref(null)
  const tokens = ref({
    access_token: localStorage.getItem('access_token'),
    refresh_token: localStorage.getItem('refresh_token')
  })
  const loading = ref(false)
  const initialized = ref(false)
  
  // Getters
  const isAuthenticated = computed(() => {
    return !!(tokens.value.access_token && user.value)
  })
  
  const isClient = computed(() => {
    return user.value?.role === 'client'
  })
  
  const isProfessional = computed(() => {
    return user.value?.role === 'professional'
  })
  
  const isAdmin = computed(() => {
    return user.value?.role === 'admin' || user.value?.role === 'owner'
  })
  
  const hasRole = computed(() => {
    return (roles) => {
      if (!user.value) return false
      if (Array.isArray(roles)) {
        return roles.includes(user.value.role)
      }
      return user.value.role === roles
    }
  })
  
  // Actions
  const setTokens = (tokenData) => {
    tokens.value = {
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token
    }
    
    // Store in localStorage
    localStorage.setItem('access_token', tokenData.access_token)
    localStorage.setItem('refresh_token', tokenData.refresh_token)
    
    // Set default authorization header
    api.defaults.headers.common['Authorization'] = `Bearer ${tokenData.access_token}`
  }
  
  const clearTokens = () => {
    tokens.value = {
      access_token: null,
      refresh_token: null
    }
    
    // Remove from localStorage
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    
    // Remove authorization header
    delete api.defaults.headers.common['Authorization']
  }
  
  const setUser = (userData) => {
    user.value = userData
  }
  
  const clearUser = () => {
    user.value = null
  }
  
  const login = async (credentials) => {
    try {
      loading.value = true
      
      const response = await api.post('/auth/login', credentials)
      const { user: userData, tokens: tokenData } = response.data
      
      setUser(userData)
      setTokens(tokenData)
      
      toast.success('Login realizado com sucesso!')
      
      // Redirect based on user role
      const redirectTo = router.currentRoute.value.query.redirect
      if (redirectTo) {
        router.push(redirectTo)
      } else if (userData.role === 'admin' || userData.role === 'owner') {
        router.push({ name: 'AdminDashboard' })
      } else if (userData.role === 'professional') {
        router.push({ name: 'ProfessionalDashboard' })
      } else {
        router.push({ name: 'ClientDashboard' })
      }
      
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || 'Erro ao fazer login'
      toast.error(message)
      return { success: false, error: message }
    } finally {
      loading.value = false
    }
  }
  
  const register = async (userData) => {
    try {
      loading.value = true
      
      const response = await api.post('/auth/register', userData)
      
      toast.success('Cadastro realizado com sucesso! Verifique seu email.')
      router.push({ name: 'Login' })
      
      return { success: true, data: response.data }
    } catch (error) {
      const message = error.response?.data?.message || 'Erro ao fazer cadastro'
      toast.error(message)
      return { success: false, error: message }
    } finally {
      loading.value = false
    }
  }
  
  const logout = async () => {
    try {
      loading.value = true
      
      // Call logout endpoint if refresh token exists
      if (tokens.value.refresh_token) {
        await api.post('/auth/logout', {
          refresh_token: tokens.value.refresh_token
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      clearTokens()
      clearUser()
      loading.value = false
      
      toast.success('Logout realizado com sucesso!')
      router.push({ name: 'Home' })
    }
  }
  
  const refreshToken = async () => {
    try {
      if (!tokens.value.refresh_token) {
        throw new Error('No refresh token available')
      }
      
      const response = await api.post('/auth/refresh', {
        refresh_token: tokens.value.refresh_token
      })
      
      const newTokens = {
        access_token: response.data.access_token,
        refresh_token: tokens.value.refresh_token // Keep existing refresh token
      }
      
      setTokens(newTokens)
      
      return true
    } catch (error) {
      console.error('Token refresh failed:', error)
      await logout()
      return false
    }
  }
  
  const fetchUser = async () => {
    try {
      if (!tokens.value.access_token) {
        return false
      }
      
      const response = await api.get('/auth/me')
      setUser(response.data)
      
      return true
    } catch (error) {
      console.error('Fetch user failed:', error)
      clearTokens()
      clearUser()
      return false
    }
  }
  
  const initialize = async () => {
    if (initialized.value) return
    
    try {
      loading.value = true
      
      // Set authorization header if token exists
      if (tokens.value.access_token) {
        api.defaults.headers.common['Authorization'] = `Bearer ${tokens.value.access_token}`
        
        // Try to fetch user data
        const success = await fetchUser()
        if (!success) {
          // Try to refresh token
          const refreshed = await refreshToken()
          if (refreshed) {
            await fetchUser()
          }
        }
      }
    } catch (error) {
      console.error('Auth initialization failed:', error)
      clearTokens()
      clearUser()
    } finally {
      loading.value = false
      initialized.value = true
    }
  }
  
  const forgotPassword = async (email) => {
    try {
      loading.value = true
      
      await api.post('/auth/forgot-password', { email })
      
      toast.success('Email de recuperação enviado!')
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || 'Erro ao enviar email de recuperação'
      toast.error(message)
      return { success: false, error: message }
    } finally {
      loading.value = false
    }
  }
  
  const resetPassword = async (token, newPassword) => {
    try {
      loading.value = true
      
      await api.post('/auth/reset-password', {
        token,
        new_password: newPassword
      })
      
      toast.success('Senha redefinida com sucesso!')
      router.push({ name: 'Login' })
      
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || 'Erro ao redefinir senha'
      toast.error(message)
      return { success: false, error: message }
    } finally {
      loading.value = false
    }
  }
  
  const verifyEmail = async (token) => {
    try {
      loading.value = true
      
      const response = await api.post('/auth/verify-email', { token })
      
      toast.success('Email verificado com sucesso!')
      
      // Update user data if logged in
      if (isAuthenticated.value) {
        setUser(response.data.user)
      }
      
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || 'Erro ao verificar email'
      toast.error(message)
      return { success: false, error: message }
    } finally {
      loading.value = false
    }
  }
  
  const updateProfile = async (profileData) => {
    try {
      loading.value = true
      
      const response = await api.put('/users/me', profileData)
      setUser(response.data)
      
      toast.success('Perfil atualizado com sucesso!')
      return { success: true, data: response.data }
    } catch (error) {
      const message = error.response?.data?.message || 'Erro ao atualizar perfil'
      toast.error(message)
      return { success: false, error: message }
    } finally {
      loading.value = false
    }
  }
  
  const changePassword = async (passwordData) => {
    try {
      loading.value = true
      
      await api.post('/auth/change-password', passwordData)
      
      toast.success('Senha alterada com sucesso!')
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || 'Erro ao alterar senha'
      toast.error(message)
      return { success: false, error: message }
    } finally {
      loading.value = false
    }
  }
  
  return {
    // State
    user,
    tokens,
    loading,
    initialized,
    
    // Getters
    isAuthenticated,
    isClient,
    isProfessional,
    isAdmin,
    hasRole,
    
    // Actions
    login,
    register,
    logout,
    refreshToken,
    fetchUser,
    initialize,
    forgotPassword,
    resetPassword,
    verifyEmail,
    updateProfile,
    changePassword,
    setUser,
    clearUser,
    setTokens,
    clearTokens
  }
})
