import axios from 'axios'
import { useToast } from 'vue-toastification'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add timestamp to prevent caching
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    // Add authorization header if token exists
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const toast = useToast()
    const originalRequest = error.config
    
    // Handle network errors
    if (!error.response) {
      toast.error('Erro de conexão. Verifique sua internet.')
      return Promise.reject(error)
    }
    
    const { status, data } = error.response
    
    // Handle 401 Unauthorized
    if (status === 401 && !originalRequest._retry) {
      originalRequest._retry = true
      
      // Try to refresh token
      const refreshToken = localStorage.getItem('refresh_token')
      if (refreshToken) {
        try {
          const response = await axios.post(
            `${api.defaults.baseURL}/auth/refresh`,
            { refresh_token: refreshToken }
          )
          
          const { access_token } = response.data
          localStorage.setItem('access_token', access_token)
          
          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access_token}`
          return api(originalRequest)
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')
          
          // Only redirect if not already on auth pages
          if (!window.location.pathname.startsWith('/auth')) {
            window.location.href = '/auth/login'
          }
          
          return Promise.reject(refreshError)
        }
      } else {
        // No refresh token, redirect to login
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        
        if (!window.location.pathname.startsWith('/auth')) {
          window.location.href = '/auth/login'
        }
      }
    }
    
    // Handle 403 Forbidden
    if (status === 403) {
      toast.error('Acesso negado. Você não tem permissão para esta ação.')
      return Promise.reject(error)
    }
    
    // Handle 404 Not Found
    if (status === 404) {
      toast.error('Recurso não encontrado.')
      return Promise.reject(error)
    }
    
    // Handle 422 Validation Error
    if (status === 422) {
      const message = data.message || 'Dados inválidos'
      toast.error(message)
      
      // Return validation errors for form handling
      if (data.details) {
        error.validationErrors = data.details
      }
      
      return Promise.reject(error)
    }
    
    // Handle 429 Too Many Requests
    if (status === 429) {
      toast.error('Muitas tentativas. Tente novamente em alguns minutos.')
      return Promise.reject(error)
    }
    
    // Handle 500 Internal Server Error
    if (status >= 500) {
      toast.error('Erro interno do servidor. Tente novamente mais tarde.')
      return Promise.reject(error)
    }
    
    // Handle other errors
    const message = data.message || 'Ocorreu um erro inesperado'
    toast.error(message)
    
    return Promise.reject(error)
  }
)

// API methods
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  logout: (refreshToken) => api.post('/auth/logout', { refresh_token: refreshToken }),
  refreshToken: (refreshToken) => api.post('/auth/refresh', { refresh_token: refreshToken }),
  me: () => api.get('/auth/me'),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (token, password) => api.post('/auth/reset-password', { token, new_password: password }),
  verifyEmail: (token) => api.post('/auth/verify-email', { token }),
  changePassword: (passwordData) => api.post('/auth/change-password', passwordData)
}

export const usersAPI = {
  getUsers: (params) => api.get('/users', { params }),
  getUser: (id) => api.get(`/users/${id}`),
  updateUser: (id, data) => api.put(`/users/${id}`, data),
  deleteUser: (id) => api.delete(`/users/${id}`),
  activateUser: (id) => api.post(`/users/${id}/activate`),
  deactivateUser: (id) => api.post(`/users/${id}/deactivate`),
  getProfile: () => api.get('/users/me'),
  updateProfile: (data) => api.put('/users/me', data),
  getStats: () => api.get('/users/stats/overview')
}

export const servicesAPI = {
  getServices: (params) => api.get('/services', { params }),
  getService: (id) => api.get(`/services/${id}`),
  createService: (data) => api.post('/services', data),
  updateService: (id, data) => api.put(`/services/${id}`, data),
  deleteService: (id) => api.delete(`/services/${id}`),
  activateService: (id) => api.post(`/services/${id}/activate`),
  deactivateService: (id) => api.post(`/services/${id}/deactivate`),
  getCategories: () => api.get('/services/categories'),
  getFeatured: (limit) => api.get('/services/featured', { params: { limit } }),
  getStats: () => api.get('/services/stats/overview')
}

export const professionalsAPI = {
  getProfessionals: (params) => api.get('/professionals', { params }),
  getProfessional: (id) => api.get(`/professionals/${id}`),
  createProfessional: (data) => api.post('/professionals', data),
  updateProfessional: (id, data) => api.put(`/professionals/${id}`, data),
  getAvailability: (id, params) => api.get(`/professionals/${id}/availability`, { params }),
  updateWorkingHours: (id, data) => api.put(`/professionals/${id}/working-hours`, data),
  createSchedule: (id, data) => api.post(`/professionals/${id}/schedule`, data),
  getStats: () => api.get('/professionals/stats/overview')
}

export const appointmentsAPI = {
  getAppointments: (params) => api.get('/appointments', { params }),
  getAppointment: (id) => api.get(`/appointments/${id}`),
  createAppointment: (data) => api.post('/appointments', data),
  updateAppointment: (id, data) => api.put(`/appointments/${id}`, data),
  confirmAppointment: (id, data) => api.post(`/appointments/${id}/confirm`, data),
  cancelAppointment: (id, data) => api.post(`/appointments/${id}/cancel`, data),
  completeAppointment: (id, data) => api.post(`/appointments/${id}/complete`, data),
  rateAppointment: (id, data) => api.post(`/appointments/${id}/rate`, data)
}

export const paymentsAPI = {
  getPayments: (params) => api.get('/payments', { params }),
  getPayment: (id) => api.get(`/payments/${id}`),
  webhook: (id, data) => api.post(`/payments/${id}/webhook`, data)
}

export const notificationsAPI = {
  getNotifications: (params) => api.get('/notifications', { params }),
  getNotification: (id) => api.get(`/notifications/${id}`),
  markAsRead: (id) => api.post(`/notifications/${id}/read`),
  markAllAsRead: () => api.post('/notifications/mark-all-read'),
  getUnreadCount: () => api.get('/notifications/unread/count')
}

// Utility functions
export const uploadFile = async (file, onProgress) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return api.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress) {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        onProgress(percentCompleted)
      }
    }
  })
}

export const downloadFile = async (url, filename) => {
  const response = await api.get(url, {
    responseType: 'blob'
  })
  
  const blob = new Blob([response.data])
  const link = document.createElement('a')
  link.href = window.URL.createObjectURL(blob)
  link.download = filename
  link.click()
  
  window.URL.revokeObjectURL(link.href)
}

export default api
