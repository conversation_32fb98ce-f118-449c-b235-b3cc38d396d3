<template>
  <div id="app" class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Loading overlay -->
    <div
      v-if="authStore.loading && !authStore.initialized"
      class="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900"
    >
      <div class="text-center">
        <div class="spinner h-12 w-12 mx-auto mb-4"></div>
        <p class="text-gray-600 dark:text-gray-400">Carregando...</p>
      </div>
    </div>

    <!-- Main app content -->
    <router-view v-else />

    <!-- Global notifications container -->
    <div id="notifications-container"></div>
  </div>
</template>

<script setup>
import { onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useDark, useToggle } from '@vueuse/core'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Dark mode
const isDark = useDark()
const toggleDark = useToggle(isDark)

// Initialize app
onMounted(async () => {
  // Initialize auth store
  await authStore.initialize()
  
  // Set up axios interceptors for token refresh
  setupAxiosInterceptors()
  
  // Set up global error handling
  setupErrorHandling()
  
  // Set up PWA
  setupPWA()
})

// Watch for route changes to update page title
watch(
  () => route.meta.title,
  (title) => {
    if (title) {
      document.title = `${title} - Salon Booking`
    } else {
      document.title = 'Salon Booking'
    }
  },
  { immediate: true }
)

// Setup axios interceptors
const setupAxiosInterceptors = () => {
  // This is handled in the api service
  // but we can add additional global interceptors here if needed
}

// Setup global error handling
const setupErrorHandling = () => {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    // Prevent the default browser error handling
    event.preventDefault()
    
    // You can send error to logging service here
    // logError(event.reason)
  })
  
  // Handle JavaScript errors
  window.addEventListener('error', (event) => {
    console.error('JavaScript error:', event.error)
    
    // You can send error to logging service here
    // logError(event.error)
  })
}

// Setup PWA
const setupPWA = () => {
  // Register service worker
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration)
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError)
      })
  }
  
  // Handle app install prompt
  let deferredPrompt
  
  window.addEventListener('beforeinstallprompt', (e) => {
    // Prevent Chrome 67 and earlier from automatically showing the prompt
    e.preventDefault()
    
    // Stash the event so it can be triggered later
    deferredPrompt = e
    
    // Show install button or banner
    showInstallPromotion()
  })
  
  window.addEventListener('appinstalled', (evt) => {
    console.log('App was installed')
    hideInstallPromotion()
  })
}

const showInstallPromotion = () => {
  // You can show a custom install promotion here
  // For example, a banner or button to install the app
}

const hideInstallPromotion = () => {
  // Hide the install promotion
}

// Provide global functions
const globalFunctions = {
  toggleDark,
  isDark
}

// Make functions available globally
window.salonApp = globalFunctions
</script>

<style>
/* Global styles are imported in main.js */

/* App-specific styles */
#app {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f4f6;
  border-top: 2px solid #ec4899;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Focus styles */
*:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

*:focus-visible {
  outline: 2px solid #ec4899;
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background-color: #ec4899;
  color: white;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  * {
    color-adjust: exact !important;
    -webkit-print-color-adjust: exact !important;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  * {
    border-color: currentColor;
  }
}
</style>
