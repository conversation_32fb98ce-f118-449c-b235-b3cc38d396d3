import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// Styles
import './assets/css/main.css'

// Toast notifications
import Toast from 'vue-toastification'
import 'vue-toastification/dist/index.css'

// Loading overlay
import { LoadingPlugin } from 'vue-loading-overlay'
import 'vue-loading-overlay/dist/css/index.css'

// Perfect scrollbar
import PerfectScrollbar from 'vue3-perfect-scrollbar'
import 'vue3-perfect-scrollbar/dist/vue3-perfect-scrollbar.css'

// Floating Vue (tooltips, popovers)
import FloatingVue from 'floating-vue'
import 'floating-vue/dist/style.css'

// Create app
const app = createApp(App)

// Create Pinia store
const pinia = createPinia()

// Toast configuration
const toastOptions = {
  position: 'top-right',
  timeout: 5000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: 'button',
  icon: true,
  rtl: false,
  transition: 'Vue-Toastification__bounce',
  maxToasts: 5,
  newestOnTop: true
}

// Use plugins
app.use(pinia)
app.use(router)
app.use(Toast, toastOptions)
app.use(LoadingPlugin)
app.use(PerfectScrollbar)
app.use(FloatingVue)

// Global properties
app.config.globalProperties.$filters = {
  currency(value) {
    if (!value) return 'R$ 0,00'
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  },
  
  date(value, format = 'short') {
    if (!value) return ''
    const date = new Date(value)
    
    if (format === 'short') {
      return date.toLocaleDateString('pt-BR')
    } else if (format === 'long') {
      return date.toLocaleDateString('pt-BR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } else if (format === 'time') {
      return date.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      })
    } else if (format === 'datetime') {
      return date.toLocaleString('pt-BR')
    }
    
    return date.toLocaleDateString('pt-BR')
  },
  
  capitalize(value) {
    if (!value) return ''
    return value.charAt(0).toUpperCase() + value.slice(1)
  },
  
  truncate(value, length = 50) {
    if (!value) return ''
    if (value.length <= length) return value
    return value.substring(0, length) + '...'
  }
}

// Error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err)
  console.error('Component:', vm)
  console.error('Info:', info)
  
  // You can send error to logging service here
  // logError(err, vm, info)
}

// Mount app
app.mount('#app')
