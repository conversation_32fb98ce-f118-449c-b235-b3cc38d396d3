import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Layouts
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import AuthLayout from '@/layouts/AuthLayout.vue'
import AdminLayout from '@/layouts/AdminLayout.vue'

// Public pages
import Home from '@/views/Home.vue'
import Services from '@/views/Services.vue'
import ServiceDetail from '@/views/ServiceDetail.vue'
import Professionals from '@/views/Professionals.vue'
import ProfessionalDetail from '@/views/ProfessionalDetail.vue'
import About from '@/views/About.vue'
import Contact from '@/views/Contact.vue'

// Auth pages
import Login from '@/views/auth/Login.vue'
import Register from '@/views/auth/Register.vue'
import ForgotPassword from '@/views/auth/ForgotPassword.vue'
import ResetPassword from '@/views/auth/ResetPassword.vue'
import VerifyEmail from '@/views/auth/VerifyEmail.vue'

// Client pages
import ClientDashboard from '@/views/client/Dashboard.vue'
import ClientAppointments from '@/views/client/Appointments.vue'
import ClientProfile from '@/views/client/Profile.vue'
import BookAppointment from '@/views/client/BookAppointment.vue'

// Professional pages
import ProfessionalDashboard from '@/views/professional/Dashboard.vue'
import ProfessionalAppointments from '@/views/professional/Appointments.vue'
import ProfessionalSchedule from '@/views/professional/Schedule.vue'
import ProfessionalProfile from '@/views/professional/Profile.vue'

// Admin pages
import AdminDashboard from '@/views/admin/Dashboard.vue'
import AdminUsers from '@/views/admin/Users.vue'
import AdminServices from '@/views/admin/Services.vue'
import AdminProfessionals from '@/views/admin/Professionals.vue'
import AdminAppointments from '@/views/admin/Appointments.vue'
import AdminReports from '@/views/admin/Reports.vue'
import AdminSettings from '@/views/admin/Settings.vue'

// Error pages
import NotFound from '@/views/errors/NotFound.vue'
import Unauthorized from '@/views/errors/Unauthorized.vue'
import ServerError from '@/views/errors/ServerError.vue'

const routes = [
  // Public routes
  {
    path: '/',
    component: DefaultLayout,
    children: [
      {
        path: '',
        name: 'Home',
        component: Home,
        meta: { title: 'Início' }
      },
      {
        path: '/services',
        name: 'Services',
        component: Services,
        meta: { title: 'Serviços' }
      },
      {
        path: '/services/:id',
        name: 'ServiceDetail',
        component: ServiceDetail,
        meta: { title: 'Detalhes do Serviço' }
      },
      {
        path: '/professionals',
        name: 'Professionals',
        component: Professionals,
        meta: { title: 'Profissionais' }
      },
      {
        path: '/professionals/:id',
        name: 'ProfessionalDetail',
        component: ProfessionalDetail,
        meta: { title: 'Profissional' }
      },
      {
        path: '/about',
        name: 'About',
        component: About,
        meta: { title: 'Sobre Nós' }
      },
      {
        path: '/contact',
        name: 'Contact',
        component: Contact,
        meta: { title: 'Contato' }
      }
    ]
  },

  // Auth routes
  {
    path: '/auth',
    component: AuthLayout,
    meta: { requiresGuest: true },
    children: [
      {
        path: 'login',
        name: 'Login',
        component: Login,
        meta: { title: 'Entrar' }
      },
      {
        path: 'register',
        name: 'Register',
        component: Register,
        meta: { title: 'Cadastrar' }
      },
      {
        path: 'forgot-password',
        name: 'ForgotPassword',
        component: ForgotPassword,
        meta: { title: 'Esqueci a Senha' }
      },
      {
        path: 'reset-password',
        name: 'ResetPassword',
        component: ResetPassword,
        meta: { title: 'Redefinir Senha' }
      },
      {
        path: 'verify-email',
        name: 'VerifyEmail',
        component: VerifyEmail,
        meta: { title: 'Verificar Email' }
      }
    ]
  },

  // Client routes
  {
    path: '/client',
    component: DefaultLayout,
    meta: { requiresAuth: true, roles: ['client'] },
    children: [
      {
        path: '',
        name: 'ClientDashboard',
        component: ClientDashboard,
        meta: { title: 'Minha Conta' }
      },
      {
        path: 'appointments',
        name: 'ClientAppointments',
        component: ClientAppointments,
        meta: { title: 'Meus Agendamentos' }
      },
      {
        path: 'book',
        name: 'BookAppointment',
        component: BookAppointment,
        meta: { title: 'Agendar Serviço' }
      },
      {
        path: 'profile',
        name: 'ClientProfile',
        component: ClientProfile,
        meta: { title: 'Meu Perfil' }
      }
    ]
  },

  // Professional routes
  {
    path: '/professional',
    component: DefaultLayout,
    meta: { requiresAuth: true, roles: ['professional'] },
    children: [
      {
        path: '',
        name: 'ProfessionalDashboard',
        component: ProfessionalDashboard,
        meta: { title: 'Dashboard Profissional' }
      },
      {
        path: 'appointments',
        name: 'ProfessionalAppointments',
        component: ProfessionalAppointments,
        meta: { title: 'Agendamentos' }
      },
      {
        path: 'schedule',
        name: 'ProfessionalSchedule',
        component: ProfessionalSchedule,
        meta: { title: 'Minha Agenda' }
      },
      {
        path: 'profile',
        name: 'ProfessionalProfile',
        component: ProfessionalProfile,
        meta: { title: 'Meu Perfil' }
      }
    ]
  },

  // Admin routes
  {
    path: '/admin',
    component: AdminLayout,
    meta: { requiresAuth: true, roles: ['admin', 'owner'] },
    children: [
      {
        path: '',
        name: 'AdminDashboard',
        component: AdminDashboard,
        meta: { title: 'Dashboard Admin' }
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: AdminUsers,
        meta: { title: 'Usuários' }
      },
      {
        path: 'services',
        name: 'AdminServices',
        component: AdminServices,
        meta: { title: 'Serviços' }
      },
      {
        path: 'professionals',
        name: 'AdminProfessionals',
        component: AdminProfessionals,
        meta: { title: 'Profissionais' }
      },
      {
        path: 'appointments',
        name: 'AdminAppointments',
        component: AdminAppointments,
        meta: { title: 'Agendamentos' }
      },
      {
        path: 'reports',
        name: 'AdminReports',
        component: AdminReports,
        meta: { title: 'Relatórios' }
      },
      {
        path: 'settings',
        name: 'AdminSettings',
        component: AdminSettings,
        meta: { title: 'Configurações' }
      }
    ]
  },

  // Error routes
  {
    path: '/unauthorized',
    name: 'Unauthorized',
    component: Unauthorized,
    meta: { title: 'Acesso Negado' }
  },
  {
    path: '/server-error',
    name: 'ServerError',
    component: ServerError,
    meta: { title: 'Erro do Servidor' }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: { title: 'Página Não Encontrada' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Set page title
  document.title = to.meta.title ? `${to.meta.title} - Salon Booking` : 'Salon Booking'
  
  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      next({ name: 'Login', query: { redirect: to.fullPath } })
      return
    }
    
    // Check role permissions
    if (to.meta.roles && !to.meta.roles.includes(authStore.user?.role)) {
      next({ name: 'Unauthorized' })
      return
    }
  }
  
  // Check if route requires guest (not authenticated)
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    // Redirect based on user role
    const role = authStore.user?.role
    if (role === 'admin' || role === 'owner') {
      next({ name: 'AdminDashboard' })
    } else if (role === 'professional') {
      next({ name: 'ProfessionalDashboard' })
    } else {
      next({ name: 'ClientDashboard' })
    }
    return
  }
  
  next()
})

export default router
