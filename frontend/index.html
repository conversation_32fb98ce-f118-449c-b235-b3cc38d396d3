<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/salon-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Sistema de agendamento online para salão de beleza" />
    <meta name="keywords" content="salão, beleza, agendamento, cabelo, estética, manicure" />
    <meta name="author" content="Salon Booking System" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://yoursalon.com/" />
    <meta property="og:title" content="Salon Booking - Agendamento Online" />
    <meta property="og:description" content="Agende seus serviços de beleza online de forma rápida e fácil" />
    <meta property="og:image" content="/salon-og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://yoursalon.com/" />
    <meta property="twitter:title" content="Salon Booking - Agendamento Online" />
    <meta property="twitter:description" content="Agende seus serviços de beleza online de forma rápida e fácil" />
    <meta property="twitter:image" content="/salon-og-image.jpg" />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- PWA -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#ec4899" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Salon Booking" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    
    <title>Salon Booking - Agendamento Online</title>
  </head>
  <body class="font-sans antialiased">
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
