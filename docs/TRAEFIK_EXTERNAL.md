# 🌐 Usando Traefik Externo com Salon Booking

Este documento explica como configurar o sistema Salon Booking para usar um container Traefik já existente.

## 📋 Pré-requisitos

### 1. Traefik Externo Configurado

Certifique-se de que seu Traefik externo está configurado com:

```yaml
# docker-compose.yml do seu Traefik
version: '3.8'

services:
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    command:
      - --api.dashboard=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - letsencrypt:/letsencrypt
    networks:
      - web

networks:
  web:
    external: true

volumes:
  letsencrypt:
```

### 2. Network Externa 'web'

Crie a network externa se ainda não existir:

```bash
docker network create web
```

## 🔧 Configuração do Salon Booking

### 1. Variáveis de Ambiente

Configure no arquivo `.env`:

```bash
# Domínio principal
DOMAIN=localhost  # Para desenvolvimento
# DOMAIN=yourdomain.com  # Para produção

# Autenticação para painéis admin
FLOWER_AUTH=admin:$2y$10$K8V2VzWzVzWzVzWzVzWzVe   # admin:admin
ADMINER_AUTH=admin:$2y$10$K8V2VzWzVzWzVzWzVzWzVe  # admin:admin

# Configurações de produção
FLOWER_USER=admin
FLOWER_PASSWORD=change-this-password
ADMIN_IPS=127.0.0.1/32,10.0.0.0/8
```

### 2. Labels do Traefik

O sistema está configurado com os seguintes labels para cada serviço:

#### Frontend (Vue.js)
```yaml
labels:
  - "traefik.enable=true"
  - "traefik.docker.network=web"
  - "traefik.http.routers.salon-frontend.rule=Host(`${DOMAIN}`)"
  - "traefik.http.routers.salon-frontend.entrypoints=websecure"
  - "traefik.http.routers.salon-frontend.tls.certresolver=letsencrypt"
  - "traefik.http.services.salon-frontend.loadbalancer.server.port=3000"
```

#### Backend (FastAPI)
```yaml
labels:
  - "traefik.enable=true"
  - "traefik.docker.network=web"
  - "traefik.http.routers.salon-backend.rule=Host(`api.${DOMAIN}`)"
  - "traefik.http.routers.salon-backend.entrypoints=websecure"
  - "traefik.http.routers.salon-backend.tls.certresolver=letsencrypt"
  - "traefik.http.services.salon-backend.loadbalancer.server.port=8000"
```

#### Flower (Celery Monitor)
```yaml
labels:
  - "traefik.enable=true"
  - "traefik.docker.network=web"
  - "traefik.http.routers.salon-flower.rule=Host(`flower.${DOMAIN}`)"
  - "traefik.http.routers.salon-flower.entrypoints=websecure"
  - "traefik.http.routers.salon-flower.tls.certresolver=letsencrypt"
  - "traefik.http.services.salon-flower.loadbalancer.server.port=5555"
  - "traefik.http.routers.salon-flower.middlewares=salon-flower-auth"
  - "traefik.http.middlewares.salon-flower-auth.basicauth.users=${FLOWER_AUTH}"
```

#### Adminer (Database Admin)
```yaml
labels:
  - "traefik.enable=true"
  - "traefik.docker.network=web"
  - "traefik.http.routers.salon-adminer.rule=Host(`adminer.${DOMAIN}`)"
  - "traefik.http.routers.salon-adminer.entrypoints=websecure"
  - "traefik.http.routers.salon-adminer.tls.certresolver=letsencrypt"
  - "traefik.http.services.salon-adminer.loadbalancer.server.port=8080"
  - "traefik.http.routers.salon-adminer.middlewares=salon-adminer-auth"
  - "traefik.http.middlewares.salon-adminer-auth.basicauth.users=${ADMINER_AUTH}"
```

## 🚀 Executando o Sistema

### 1. Configuração Inicial

```bash
# Copiar arquivo de configuração
cp .env.example .env

# Editar configurações
nano .env

# Configurar domínios locais (desenvolvimento)
sudo ./scripts/setup-local-domains.sh
```

### 2. Iniciar Serviços

```bash
# Desenvolvimento
make dev

# Ou manualmente
docker-compose up -d
```

### 3. Verificar Conectividade

```bash
# Verificar se os containers estão na network 'web'
docker network inspect web

# Verificar logs
make logs

# Testar saúde dos serviços
make health
```

## 🌐 Domínios Configurados

### Desenvolvimento (localhost)
- **Frontend**: https://localhost
- **Backend API**: https://api.localhost
- **Docs API**: https://api.localhost/docs
- **Flower**: https://flower.localhost (admin:admin)
- **Adminer**: https://adminer.localhost (admin:admin)

### Produção (substitua pelo seu domínio)
- **Frontend**: https://yourdomain.com
- **Backend API**: https://api.yourdomain.com
- **Docs API**: https://api.yourdomain.com/docs
- **Flower**: https://flower.yourdomain.com
- **Adminer**: https://adminer.yourdomain.com

## 🔧 Troubleshooting

### 1. Serviços não são descobertos pelo Traefik

```bash
# Verificar se os containers estão na network 'web'
docker network inspect web

# Verificar labels dos containers
docker inspect salon_frontend | grep -A 20 Labels
docker inspect salon_backend | grep -A 20 Labels
```

### 2. Conflitos de nomes de routers

Se você tiver outros serviços usando o Traefik, pode haver conflitos de nomes. Os routers do Salon Booking usam prefixo `salon-`:

- `salon-frontend`
- `salon-backend`
- `salon-flower`
- `salon-adminer`

### 3. Certificados SSL não funcionam

Verifique se seu Traefik externo está configurado com Let's Encrypt:

```bash
# Verificar logs do Traefik
docker logs traefik | grep -i acme

# Verificar certificados
docker exec traefik ls -la /letsencrypt/
```

### 4. Problemas de CORS

Se houver problemas de CORS, verifique se o backend está configurado corretamente:

```bash
# Verificar variáveis de ambiente do backend
docker exec salon_backend env | grep CORS
```

## 📊 Monitoramento

### Dashboard do Traefik

Se seu Traefik externo tem dashboard habilitado, você pode monitorar os serviços do Salon Booking através dele.

### Logs

```bash
# Logs do Salon Booking
make logs

# Logs específicos
make logs-backend
make logs-frontend

# Logs do Traefik externo
docker logs -f traefik
```

### Status dos Serviços

```bash
# Status dos containers
make status

# Verificar saúde
make health

# Monitorar recursos
make monitor
```

## 🔒 Segurança

### 1. Autenticação Básica

Os painéis administrativos (Flower e Adminer) estão protegidos com autenticação básica. Para gerar novas senhas:

```bash
# Gerar hash bcrypt
htpasswd -nb admin sua_nova_senha

# Ou usando Python
python3 -c "import bcrypt; print('admin:' + bcrypt.hashpw(b'sua_senha', bcrypt.gensalt()).decode())"
```

### 2. Restrição de IP (Produção)

Para produção, configure a variável `ADMIN_IPS` para restringir acesso aos painéis administrativos:

```bash
# Exemplo: apenas IPs específicos
ADMIN_IPS=***********/32,************/24

# Exemplo: rede interna
ADMIN_IPS=10.0.0.0/8,**********/12,***********/16
```

### 3. Desabilitar Serviços em Produção

Para produção, você pode desabilitar o Adminer:

```bash
# No docker-compose.prod.yml, o Adminer está configurado com profile 'debug'
# Para habilitá-lo em produção:
docker-compose --profile debug up -d
```

## 📚 Comandos Úteis

```bash
# Verificar network
docker network ls
docker network inspect web

# Verificar containers na network
docker network inspect web --format '{{range .Containers}}{{.Name}} {{end}}'

# Reiniciar apenas serviços do Salon Booking
make restart

# Reconstruir imagens
make build

# Backup do banco
make backup

# Limpar ambiente
make clean
```

## 🔄 Atualizações

Para atualizar o sistema mantendo o Traefik externo:

```bash
# Atualizar código
git pull

# Reconstruir e reiniciar
make update

# Ou manualmente
docker-compose build
docker-compose up -d
```

---

💡 **Dica**: Mantenha os nomes dos routers únicos usando o prefixo `salon-` para evitar conflitos com outros serviços no mesmo Traefik.
