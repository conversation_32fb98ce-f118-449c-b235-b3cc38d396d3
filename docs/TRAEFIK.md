# 🚀 Traefik Configuration Guide

Este documento explica como o Traefik está configurado no projeto Salon Booking e como utilizá-lo.

## 📋 O que é o Traefik?

O Traefik é um proxy reverso moderno e load balancer que facilita o deploy de microsserviços. Ele automaticamente descobre serviços e configura roteamento, SSL/TLS e muito mais.

## 🏗️ Arquitetura

```
Internet → Traefik (Port 80/443) → Services
                ↓
        ┌─────────────────┐
        │     Traefik     │
        │   (Port 80/443) │
        └─────────────────┘
                ↓
    ┌───────────┬───────────┬───────────┐
    │ Frontend  │  Backend  │  Flower   │
    │   :3000   │   :8000   │   :5555   │
    └───────────┴───────────┴───────────┘
```

## 🌐 Domínios Configurados

### Desenvolvimento (localhost)
- **Frontend**: https://localhost
- **Backend API**: https://api.localhost
- **Traefik Dashboard**: https://traefik.localhost
- **Flower (Celery)**: https://flower.localhost
- **Adminer (DB)**: https://adminer.localhost

### Produção (substitua `yourdomain.com`)
- **Frontend**: https://yourdomain.com
- **Backend API**: https://api.yourdomain.com
- **Traefik Dashboard**: https://traefik.yourdomain.com
- **Flower (Celery)**: https://flower.yourdomain.com
- **Adminer (DB)**: https://adminer.yourdomain.com

## ⚙️ Configuração

### 1. Variáveis de Ambiente

Configure no arquivo `.env`:

```bash
# Domínio principal
DOMAIN=localhost  # Para desenvolvimento
# DOMAIN=yourdomain.com  # Para produção

# Email para Let's Encrypt
ACME_EMAIL=<EMAIL>

# Autenticação básica (usuário:senha em hash bcrypt)
TRAEFIK_AUTH=admin:$2y$10$K8V2VzWzVzWzVzWzVzWzVe  # admin:admin
FLOWER_AUTH=admin:$2y$10$K8V2VzWzVzWzVzWzVzWzVe   # admin:admin
ADMINER_AUTH=admin:$2y$10$K8V2VzWzVzWzVzWzVzWzVe  # admin:admin
```

### 2. Gerar Hash de Senha

Para gerar um hash bcrypt para autenticação:

```bash
# Usando htpasswd
htpasswd -nb admin sua_senha

# Usando Python
python3 -c "import bcrypt; print('admin:' + bcrypt.hashpw(b'sua_senha', bcrypt.gensalt()).decode())"

# Usando online (não recomendado para produção)
# https://bcrypt-generator.com/
```

## 🚀 Executando

### Desenvolvimento

```bash
# Configurar domínio local (opcional)
echo "127.0.0.1 localhost api.localhost traefik.localhost flower.localhost adminer.localhost" | sudo tee -a /etc/hosts

# Iniciar serviços
docker-compose up -d

# Verificar logs do Traefik
docker-compose logs -f traefik
```

### Produção

```bash
# Configurar variáveis de produção
cp .env.example .env
# Editar .env com suas configurações

# Configurar DNS
# Aponte os seguintes registros para seu servidor:
# A     yourdomain.com        → IP_DO_SERVIDOR
# CNAME api.yourdomain.com    → yourdomain.com
# CNAME traefik.yourdomain.com → yourdomain.com
# CNAME flower.yourdomain.com → yourdomain.com
# CNAME adminer.yourdomain.com → yourdomain.com

# Iniciar em produção
docker-compose up -d
```

## 🔒 SSL/TLS Automático

O Traefik automaticamente:

1. **Obtém certificados** Let's Encrypt para todos os domínios
2. **Renova certificados** automaticamente antes do vencimento
3. **Redireciona HTTP** para HTTPS automaticamente
4. **Configura HSTS** e outras headers de segurança

### Certificados de Desenvolvimento

Para desenvolvimento local com HTTPS, você pode:

1. **Usar certificados auto-assinados** (navegador mostrará aviso)
2. **Configurar mkcert** para certificados locais confiáveis:

```bash
# Instalar mkcert
brew install mkcert  # macOS
# ou
sudo apt install mkcert  # Ubuntu

# Configurar CA local
mkcert -install

# Gerar certificados
mkcert localhost api.localhost traefik.localhost flower.localhost adminer.localhost
```

## 📊 Monitoramento

### Dashboard do Traefik

Acesse: https://traefik.localhost (ou traefik.yourdomain.com)

- **Usuário**: admin
- **Senha**: admin (altere em produção!)

O dashboard mostra:
- Serviços descobertos
- Rotas configuradas
- Status de saúde
- Certificados SSL
- Métricas em tempo real

### Logs

```bash
# Logs do Traefik
docker-compose logs -f traefik

# Logs de acesso
docker-compose exec traefik cat /var/log/traefik/access.log

# Logs de erro
docker-compose exec traefik cat /var/log/traefik/traefik.log
```

## 🛡️ Segurança

### Headers de Segurança

O Traefik automaticamente adiciona:

```yaml
# Configurado via labels no docker-compose.yml
- "traefik.http.middlewares.security.headers.frameDeny=true"
- "traefik.http.middlewares.security.headers.sslRedirect=true"
- "traefik.http.middlewares.security.headers.browserXssFilter=true"
- "traefik.http.middlewares.security.headers.contentTypeNosniff=true"
- "traefik.http.middlewares.security.headers.forceSTSHeader=true"
- "traefik.http.middlewares.security.headers.stsIncludeSubdomains=true"
- "traefik.http.middlewares.security.headers.stsPreload=true"
- "traefik.http.middlewares.security.headers.stsSeconds=31536000"
```

### Rate Limiting

```yaml
# Exemplo de rate limiting
- "traefik.http.middlewares.ratelimit.ratelimit.burst=100"
- "traefik.http.middlewares.ratelimit.ratelimit.average=50"
```

### IP Whitelist

```yaml
# Restringir acesso por IP
- "traefik.http.middlewares.ipwhitelist.ipwhitelist.sourcerange=***********/24,10.0.0.0/8"
```

## 🔧 Troubleshooting

### Problemas Comuns

1. **Certificado SSL não funciona**
   ```bash
   # Verificar logs do ACME
   docker-compose logs traefik | grep acme
   
   # Verificar se o domínio está acessível externamente
   curl -I http://yourdomain.com
   ```

2. **Serviço não é descoberto**
   ```bash
   # Verificar se o container está na rede correta
   docker network ls
   docker network inspect salon_network
   
   # Verificar labels do container
   docker inspect container_name | grep -A 20 Labels
   ```

3. **Dashboard não carrega**
   ```bash
   # Verificar se a porta 8080 está exposta
   docker-compose ps traefik
   
   # Verificar configuração de autenticação
   echo "admin:admin" | base64
   ```

### Comandos Úteis

```bash
# Verificar configuração do Traefik
docker-compose exec traefik traefik version

# Recarregar configuração
docker-compose restart traefik

# Verificar certificados
docker-compose exec traefik ls -la /letsencrypt/

# Testar conectividade
curl -H "Host: api.localhost" http://localhost/health
```

## 📈 Performance

### Cache

```yaml
# Configurar cache para assets estáticos
- "traefik.http.middlewares.cache.headers.customrequestheaders.Cache-Control=public, max-age=3600"
```

### Compressão

```yaml
# Habilitar compressão gzip
- "traefik.http.middlewares.compress.compress=true"
```

### Load Balancing

```yaml
# Configurar múltiplas instâncias
- "traefik.http.services.backend.loadbalancer.server.port=8000"
- "traefik.http.services.backend.loadbalancer.healthcheck.path=/health"
- "traefik.http.services.backend.loadbalancer.healthcheck.interval=30s"
```

## 🔄 Backup e Restore

### Backup dos Certificados

```bash
# Backup do volume de certificados
docker run --rm -v salon_traefik_letsencrypt:/data -v $(pwd):/backup alpine tar czf /backup/certificates.tar.gz -C /data .
```

### Restore dos Certificados

```bash
# Restore do volume de certificados
docker run --rm -v salon_traefik_letsencrypt:/data -v $(pwd):/backup alpine tar xzf /backup/certificates.tar.gz -C /data
```

## 📚 Recursos Adicionais

- [Documentação Oficial do Traefik](https://doc.traefik.io/traefik/)
- [Traefik com Docker](https://doc.traefik.io/traefik/providers/docker/)
- [Let's Encrypt com Traefik](https://doc.traefik.io/traefik/https/acme/)
- [Middlewares do Traefik](https://doc.traefik.io/traefik/middlewares/overview/)

---

💡 **Dica**: Para produção, sempre use certificados SSL válidos e configure adequadamente as variáveis de ambiente de segurança!
