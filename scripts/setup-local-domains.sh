#!/bin/bash

# Script para configurar domínios locais para desenvolvimento
# Execute com: sudo ./scripts/setup-local-domains.sh

set -e

HOSTS_FILE="/etc/hosts"
DOMAINS=(
    "localhost"
    "api.localhost"
    "traefik.localhost"
    "flower.localhost"
    "adminer.localhost"
)

echo "🚀 Configurando domínios locais para desenvolvimento..."

# Backup do arquivo hosts
if [ ! -f "${HOSTS_FILE}.backup" ]; then
    echo "📋 Criando backup do arquivo hosts..."
    cp "$HOSTS_FILE" "${HOSTS_FILE}.backup"
fi

# Remover entradas antigas do salon booking (se existirem)
echo "🧹 Removendo entradas antigas..."
sed -i.tmp '/# Salon Booking - Start/,/# Salon Booking - End/d' "$HOSTS_FILE"

# Adicionar novas entradas
echo "➕ Adicionando novos domínios..."
{
    echo ""
    echo "# Salon Booking - Start"
    echo "# Domínios locais para desenvolvimento"
    for domain in "${DOMAINS[@]}"; do
        echo "127.0.0.1    $domain"
    done
    echo "# Salon Booking - End"
} >> "$HOSTS_FILE"

echo "✅ Domínios configurados com sucesso!"
echo ""
echo "📋 Domínios disponíveis:"
for domain in "${DOMAINS[@]}"; do
    echo "   https://$domain"
done

echo ""
echo "🔧 Para remover os domínios, execute:"
echo "   sudo ./scripts/remove-local-domains.sh"
echo ""
echo "🚀 Agora você pode executar:"
echo "   docker-compose up -d"
echo ""
echo "⚠️  Nota: O navegador pode mostrar aviso de certificado inválido"
echo "   para HTTPS em desenvolvimento. Isso é normal."
