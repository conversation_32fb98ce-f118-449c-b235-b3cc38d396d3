#!/bin/bash

# Script para remover domínios locais configurados para desenvolvimento
# Execute com: sudo ./scripts/remove-local-domains.sh

set -e

HOSTS_FILE="/etc/hosts"

echo "🧹 Removendo domínios locais do Salon Booking..."

# Verificar se existe backup
if [ ! -f "${HOSTS_FILE}.backup" ]; then
    echo "⚠️  Backup não encontrado. Removendo apenas as entradas do Salon Booking..."
    sed -i.tmp '/# Salon Booking - Start/,/# Salon Booking - End/d' "$HOSTS_FILE"
else
    echo "📋 Restaurando backup do arquivo hosts..."
    cp "${HOSTS_FILE}.backup" "$HOSTS_FILE"
    rm "${HOSTS_FILE}.backup"
fi

echo "✅ Domínios removidos com sucesso!"
echo ""
echo "🔧 Para configurar novamente, execute:"
echo "   sudo ./scripts/setup-local-domains.sh"
