#!/bin/bash

# Script para configurar ambiente de produção
# Execute com: sudo ./scripts/setup-production.sh

set -e

echo "🚀 Configurando ambiente de produção do Salon Booking..."

# Verificar se está executando como root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Este script deve ser executado como root (use sudo)"
    exit 1
fi

# Criar diretórios de dados
echo "📁 Criando diretórios de dados..."
mkdir -p /var/lib/salon/{postgres,redis,uploads,celery,letsencrypt}

# Configurar permissões
echo "🔒 Configurando permissões..."
chown -R 999:999 /var/lib/salon/postgres     # PostgreSQL user
chown -R 999:999 /var/lib/salon/redis        # Redis user
chown -R 1001:1001 /var/lib/salon/uploads    # App user
chown -R 1001:1001 /var/lib/salon/celery     # App user

# Configurar permissões de segurança
chmod 750 /var/lib/salon/postgres
chmod 750 /var/lib/salon/redis
chmod 755 /var/lib/salon/uploads
chmod 755 /var/lib/salon/celery

# Verificar se o arquivo .env existe
if [ ! -f ".env" ]; then
    echo "📋 Criando arquivo .env a partir do exemplo..."
    cp .env.example .env
    echo "⚠️  IMPORTANTE: Edite o arquivo .env com suas configurações de produção!"
fi

# Verificar network do Traefik
if ! docker network ls | grep -q "web"; then
    echo "🌐 Criando network 'web' para o Traefik..."
    docker network create web
fi

# Verificar se o Traefik está rodando
if ! docker ps | grep -q traefik; then
    echo "⚠️  AVISO: Container Traefik não encontrado!"
    echo "   Certifique-se de que o Traefik está rodando e conectado à network 'web'"
fi

echo ""
echo "✅ Configuração de produção concluída!"
echo ""
echo "📋 Próximos passos:"
echo "1. Edite o arquivo .env com suas configurações:"
echo "   - DOMAIN=yourdomain.com"
echo "   - Senhas de produção"
echo "   - Configurações de email/SMS"
echo ""
echo "2. Inicie o ambiente de produção:"
echo "   make prod-secure"
echo ""
echo "3. Verifique os logs:"
echo "   make logs"
echo ""
echo "4. Configure backup automático:"
echo "   crontab -e"
echo "   # Adicione: 0 2 * * * cd /path/to/salon && make backup"
echo ""
echo "🔒 Diretórios criados em /var/lib/salon/:"
ls -la /var/lib/salon/
