Objetivo:
Desenvolver um sistema completo de agendamento online para um salão de beleza, incluindo frontend, backend e integrações essenciais. O sistema deve ser intuitivo para clientes e administradores, com recursos de automação e gestão.
Requisitos Técnicos:

    Tecnologias:

        Frontend: Vue.js (interface responsiva).

        Backend: Python (FastAPI).

        Banco de dados: PostgreSQL.

        Infraestrutura: Docker

    Autenticação:

        Login para clientes (e-mail/senha ou redes sociais).

        Painel administrativo para funcionários e donos.

    Integrações:

        Pagamentos: Mercado <PERSON>, Stripe ou Pix.

        Notificações: WhatsApp, e-mail (SendGrid) e SMS (Twilio).

        Google Calendar para sincronização.

Funcionalidades Principais:
Para Clientes:

    Agendamento Online:

        Selecionar serviço (corte, coloração, manicure, etc.), profissional, data/horário.

        Visualizar disponibilidade em tempo real.

        Cadastro de perfil com histórico de agendamentos.

    Lembretes e Confirmações:

        Notificações automáticas (24h antes do agendamento).

        Opção de cancelamento ou reagendamento.

    Pagamento Online:

        Pagamento parcial ou total via integração.

Para Administradores/Funcionários:

    Dashboard de Gestão:

        Visualizar agenda diária/semanal.

        Gerenciar horários (bloquear intervalos, folgas).

        Cadastrar serviços, profissionais e preços.

    Relatórios:

        Faturamento, serviços mais populares, frequência de clientes.

    Personalização:

        Definir tempo padrão por serviço, intervalo entre atendimentos.

Experiência do Usuário (UX):

    Interface limpa e moderna (inspirada em salões de beleza).

    Fluxo de agendamento em 3 passos (serviço → profissional → horário).

    Dark mode e acessibilidade (WCAG).

Extras (Opcionais):

    Fidelidade: Sistema de pontos ou descontos.

    Lista de espera para horários indisponíveis.

    API para integração com site ou Instagram.

Saída Esperada:

    Código Fonte (repositório GitHub com documentação).

    Instruções de Deploy (passo a passo para implantação).

    Protótipo de UI (Figma ou imagem).

Observação: Priorizar segurança (proteção de dados pessoais e pagamentos) e escalabilidade (suporte a múltiplos profissionais/filiais).
