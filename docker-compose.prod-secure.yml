# Docker Compose - Secure Production Overrides
# Use this file for production deployments where database ports should not be exposed
version: '3.8'

services:
  # PostgreSQL - Secure Production (no exposed ports)
  postgres:
    ports: []
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Redis - Secure Production (no exposed ports)
  redis:
    ports: []
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
        reservations:
          memory: 256M
          cpus: '0.1'

  # Backend - Production Configuration
  backend:
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=INFO
    command: gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Frontend - Production Configuration
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    environment:
      - VITE_APP_ENV=production
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
        reservations:
          memory: 256M
          cpus: '0.1'

  # Celery Worker - Production Configuration
  celery_worker:
    command: celery -A app.core.celery worker --loglevel=warning --concurrency=4
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Celery Beat - Production Configuration
  celery_beat:
    command: celery -A app.core.celery beat --loglevel=warning
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.1'
        reservations:
          memory: 128M
          cpus: '0.05'

  # Flower - Production Configuration with Security
  flower:
    command: celery -A app.core.celery flower --port=5555 --basic_auth=${FLOWER_USER}:${FLOWER_PASSWORD}
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.1'
        reservations:
          memory: 128M
          cpus: '0.05'
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=web"
      - "traefik.http.routers.salon-flower.rule=Host(`flower.${DOMAIN}`)"
      - "traefik.http.routers.salon-flower.entrypoints=websecure"
      - "traefik.http.routers.salon-flower.tls.certresolver=letsencrypt"
      - "traefik.http.services.salon-flower.loadbalancer.server.port=5555"
      # Security: IP whitelist and basic auth
      - "traefik.http.routers.salon-flower.middlewares=salon-flower-security"
      - "traefik.http.middlewares.salon-flower-security.chain.middlewares=salon-flower-ipwhitelist,salon-flower-auth"
      - "traefik.http.middlewares.salon-flower-ipwhitelist.ipwhitelist.sourcerange=${ADMIN_IPS:-127.0.0.1/32}"
      - "traefik.http.middlewares.salon-flower-auth.basicauth.users=${FLOWER_AUTH:-admin:$$2y$$10$$K8V2VzWzVzWzVzWzVzWzVe}"

  # Disable Adminer in production for security
  adminer:
    profiles:
      - debug

# Production-specific volumes with persistent storage
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/salon/postgres
  
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/salon/redis
  
  backend_uploads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/salon/uploads
  
  celery_beat_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/salon/celery
