# Salon Booking System - Makefile
# Comandos úteis para desenvolvimento e deploy

.PHONY: help dev prod stop clean logs setup-domains remove-domains backup restore test

# Cores para output
RED=\033[0;31m
GREEN=\033[0;32m
YELLOW=\033[1;33m
BLUE=\033[0;34m
NC=\033[0m # No Color

help: ## Mostra esta ajuda
	@echo "$(BLUE)Salon Booking System - Comandos Disponíveis$(NC)"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "$(GREEN)%-20s$(NC) %s\n", $$1, $$2}'

dev: ## Inicia ambiente de desenvolvimento
	@echo "$(YELLOW)🚀 Iniciando ambiente de desenvolvimento...$(NC)"
	@cp -n .env.example .env 2>/dev/null || true
	@docker-compose up -d
	@echo "$(GREEN)✅ Ambiente iniciado!$(NC)"
	@echo ""
	@echo "$(BLUE)📋 Serviços disponíveis:$(NC)"
	@echo "   Frontend:     https://localhost"
	@echo "   Backend API:  https://api.localhost"
	@echo "   Docs API:     https://api.localhost/docs"
	@echo "   Flower:       https://flower.localhost (admin:admin)"
	@echo "   Adminer:      https://adminer.localhost (admin:admin)"
	@echo ""
	@echo "$(YELLOW)⚠️  Certifique-se de que o Traefik externo está configurado para a network 'web'$(NC)"

prod: ## Inicia ambiente de produção
	@echo "$(YELLOW)🚀 Iniciando ambiente de produção...$(NC)"
	@docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
	@echo "$(GREEN)✅ Ambiente de produção iniciado!$(NC)"

prod-secure: ## Inicia ambiente de produção seguro (sem portas expostas)
	@echo "$(YELLOW)🚀 Iniciando ambiente de produção seguro...$(NC)"
	@echo "$(YELLOW)⚠️  Certifique-se de que os diretórios de dados existem:$(NC)"
	@echo "   sudo mkdir -p /var/lib/salon/{postgres,redis,uploads,celery}"
	@echo "   sudo chown -R 1001:1001 /var/lib/salon/"
	@docker-compose -f docker-compose.yml -f docker-compose.prod-secure.yml up -d
	@echo "$(GREEN)✅ Ambiente de produção seguro iniciado!$(NC)"

stop: ## Para todos os serviços
	@echo "$(YELLOW)⏹️  Parando serviços...$(NC)"
	@docker-compose down
	@echo "$(GREEN)✅ Serviços parados!$(NC)"

clean: ## Remove containers, volumes e imagens
	@echo "$(RED)🧹 Limpando ambiente...$(NC)"
	@docker-compose down -v --rmi all --remove-orphans
	@docker system prune -f
	@echo "$(GREEN)✅ Ambiente limpo!$(NC)"

logs: ## Mostra logs de todos os serviços
	@docker-compose logs -f

logs-traefik: ## Mostra logs do Traefik (container externo)
	@echo "$(YELLOW)Para ver logs do Traefik, use: docker logs -f <traefik_container_name>$(NC)"

logs-backend: ## Mostra logs do backend
	@docker-compose logs -f backend

logs-frontend: ## Mostra logs do frontend
	@docker-compose logs -f frontend

setup-domains: ## Configura domínios locais (requer sudo)
	@echo "$(YELLOW)🌐 Configurando domínios locais...$(NC)"
	@sudo ./scripts/setup-local-domains.sh

remove-domains: ## Remove domínios locais (requer sudo)
	@echo "$(YELLOW)🧹 Removendo domínios locais...$(NC)"
	@sudo ./scripts/remove-local-domains.sh

status: ## Mostra status dos serviços
	@echo "$(BLUE)📊 Status dos serviços:$(NC)"
	@docker-compose ps

restart: ## Reinicia todos os serviços
	@echo "$(YELLOW)🔄 Reiniciando serviços...$(NC)"
	@docker-compose restart
	@echo "$(GREEN)✅ Serviços reiniciados!$(NC)"

restart-traefik: ## Reinicia apenas o Traefik (container externo)
	@echo "$(YELLOW)Para reiniciar o Traefik, use: docker restart <traefik_container_name>$(NC)"

restart-backend: ## Reinicia apenas o backend
	@docker-compose restart backend

restart-frontend: ## Reinicia apenas o frontend
	@docker-compose restart frontend

build: ## Reconstrói todas as imagens
	@echo "$(YELLOW)🔨 Reconstruindo imagens...$(NC)"
	@docker-compose build --no-cache
	@echo "$(GREEN)✅ Imagens reconstruídas!$(NC)"

build-backend: ## Reconstrói apenas a imagem do backend
	@docker-compose build --no-cache backend

build-frontend: ## Reconstrói apenas a imagem do frontend
	@docker-compose build --no-cache frontend

shell-backend: ## Acessa shell do container backend
	@docker-compose exec backend bash

shell-frontend: ## Acessa shell do container frontend
	@docker-compose exec frontend sh

shell-postgres: ## Acessa shell do PostgreSQL
	@docker-compose exec postgres psql -U salon_user -d salon_db

backup: ## Faz backup do banco de dados
	@echo "$(YELLOW)💾 Fazendo backup do banco...$(NC)"
	@mkdir -p backups
	@docker-compose exec postgres pg_dump -U salon_user salon_db > backups/salon_db_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)✅ Backup criado em backups/$(NC)"

restore: ## Restaura backup do banco (uso: make restore FILE=backup.sql)
	@echo "$(YELLOW)📥 Restaurando backup...$(NC)"
	@docker-compose exec -T postgres psql -U salon_user -d salon_db < $(FILE)
	@echo "$(GREEN)✅ Backup restaurado!$(NC)"

migrate: ## Executa migrações do banco
	@echo "$(YELLOW)🗄️  Executando migrações...$(NC)"
	@docker-compose exec backend alembic upgrade head
	@echo "$(GREEN)✅ Migrações executadas!$(NC)"

test: ## Executa testes
	@echo "$(YELLOW)🧪 Executando testes...$(NC)"
	@docker-compose exec backend pytest
	@echo "$(GREEN)✅ Testes executados!$(NC)"

test-frontend: ## Executa testes do frontend
	@echo "$(YELLOW)🧪 Executando testes do frontend...$(NC)"
	@docker-compose exec frontend npm test
	@echo "$(GREEN)✅ Testes do frontend executados!$(NC)"

lint: ## Executa linting
	@echo "$(YELLOW)🔍 Executando linting...$(NC)"
	@docker-compose exec backend flake8 app/
	@docker-compose exec frontend npm run lint
	@echo "$(GREEN)✅ Linting executado!$(NC)"

format: ## Formata código
	@echo "$(YELLOW)✨ Formatando código...$(NC)"
	@docker-compose exec backend black app/
	@docker-compose exec frontend npm run format
	@echo "$(GREEN)✅ Código formatado!$(NC)"

install: ## Instalação inicial completa
	@echo "$(BLUE)🎯 Instalação inicial do Salon Booking System$(NC)"
	@echo ""
	@echo "$(YELLOW)1. Copiando arquivo de configuração...$(NC)"
	@cp -n .env.example .env 2>/dev/null || true
	@echo "$(YELLOW)2. Configurando domínios locais...$(NC)"
	@sudo ./scripts/setup-local-domains.sh
	@echo "$(YELLOW)3. Construindo imagens...$(NC)"
	@docker-compose build
	@echo "$(YELLOW)4. Iniciando serviços...$(NC)"
	@docker-compose up -d
	@echo "$(YELLOW)5. Aguardando serviços ficarem prontos...$(NC)"
	@sleep 30
	@echo "$(YELLOW)6. Executando migrações...$(NC)"
	@docker-compose exec backend alembic upgrade head
	@echo ""
	@echo "$(GREEN)🎉 Instalação concluída com sucesso!$(NC)"
	@echo ""
	@echo "$(BLUE)📋 Acesse os serviços:$(NC)"
	@echo "   Frontend:     https://localhost"
	@echo "   Backend API:  https://api.localhost"
	@echo "   Docs API:     https://api.localhost/docs"
	@echo "   Flower:       https://flower.localhost (admin:admin)"
	@echo "   Adminer:      https://adminer.localhost (admin:admin)"

uninstall: ## Remove instalação completa
	@echo "$(RED)🗑️  Removendo instalação completa...$(NC)"
	@docker-compose down -v --rmi all --remove-orphans
	@sudo ./scripts/remove-local-domains.sh
	@docker system prune -f
	@echo "$(GREEN)✅ Instalação removida!$(NC)"

update: ## Atualiza o sistema
	@echo "$(YELLOW)🔄 Atualizando sistema...$(NC)"
	@git pull
	@docker-compose build
	@docker-compose up -d
	@docker-compose exec backend alembic upgrade head
	@echo "$(GREEN)✅ Sistema atualizado!$(NC)"

# Comandos de monitoramento
monitor: ## Mostra uso de recursos
	@echo "$(BLUE)📊 Monitoramento de recursos:$(NC)"
	@docker stats --no-stream

health: ## Verifica saúde dos serviços
	@echo "$(BLUE)🏥 Verificando saúde dos serviços:$(NC)"
	@curl -s https://localhost/ || echo "❌ Frontend indisponível"
	@curl -s https://api.localhost/health || echo "❌ Backend indisponível"
