# Traefik Static Configuration
# This file is optional - configuration is mainly done via Docker labels

global:
  checkNewVersion: false
  sendAnonymousUsage: false

api:
  dashboard: true
  debug: true
  insecure: false

log:
  level: INFO
  format: json

accessLog:
  format: json

entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entrypoint:
          to: websecure
          scheme: https
          permanent: true

  websecure:
    address: ":443"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: salon_network
    watch: true

certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /letsencrypt/acme.json
      caServer: https://acme-v02.api.letsencrypt.org/directory
      tlsChallenge: {}

# Metrics (optional)
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true

# Tracing (optional)
tracing:
  jaeger:
    samplingServerURL: http://jaeger:14268/api/sampling
    localAgentHostPort: jaeger:6831
