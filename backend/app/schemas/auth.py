"""
Authentication schemas for API serialization.
"""

from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

from app.schemas.user import UserResponse


class Token(BaseModel):
    """Token response schema."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int  # seconds


class TokenData(BaseModel):
    """Token data schema."""
    user_id: Optional[str] = None


class LoginResponse(BaseModel):
    """Login response schema."""
    user: UserResponse
    tokens: Token
    message: str = "Login successful"


class RefreshTokenRequest(BaseModel):
    """Refresh token request schema."""
    refresh_token: str


class RefreshTokenResponse(BaseModel):
    """Refresh token response schema."""
    access_token: str
    token_type: str = "bearer"
    expires_in: int


class LogoutRequest(BaseModel):
    """Logout request schema."""
    refresh_token: Optional[str] = None


class LogoutResponse(BaseModel):
    """Logout response schema."""
    message: str = "Logout successful"


class GoogleAuthRequest(BaseModel):
    """Google authentication request schema."""
    code: str
    redirect_uri: str


class GoogleAuthResponse(BaseModel):
    """Google authentication response schema."""
    user: UserResponse
    tokens: Token
    is_new_user: bool
    message: str


class FacebookAuthRequest(BaseModel):
    """Facebook authentication request schema."""
    access_token: str


class FacebookAuthResponse(BaseModel):
    """Facebook authentication response schema."""
    user: UserResponse
    tokens: Token
    is_new_user: bool
    message: str


class VerifyTokenRequest(BaseModel):
    """Verify token request schema."""
    token: str


class VerifyTokenResponse(BaseModel):
    """Verify token response schema."""
    valid: bool
    user_id: Optional[str] = None
    expires_at: Optional[datetime] = None


class ChangePasswordRequest(BaseModel):
    """Change password request schema."""
    current_password: str
    new_password: str


class ChangePasswordResponse(BaseModel):
    """Change password response schema."""
    message: str = "Password changed successfully"


class ForgotPasswordRequest(BaseModel):
    """Forgot password request schema."""
    email: EmailStr


class ForgotPasswordResponse(BaseModel):
    """Forgot password response schema."""
    message: str = "Password reset email sent"


class ResetPasswordRequest(BaseModel):
    """Reset password request schema."""
    token: str
    new_password: str


class ResetPasswordResponse(BaseModel):
    """Reset password response schema."""
    message: str = "Password reset successfully"


class ResendVerificationRequest(BaseModel):
    """Resend verification email request schema."""
    email: EmailStr


class ResendVerificationResponse(BaseModel):
    """Resend verification email response schema."""
    message: str = "Verification email sent"


class VerifyEmailRequest(BaseModel):
    """Verify email request schema."""
    token: str


class VerifyEmailResponse(BaseModel):
    """Verify email response schema."""
    message: str = "Email verified successfully"
    user: UserResponse


class AuthStatus(BaseModel):
    """Authentication status schema."""
    authenticated: bool
    user: Optional[UserResponse] = None
    permissions: Optional[list] = None


class SessionInfo(BaseModel):
    """Session information schema."""
    session_id: str
    user_id: str
    created_at: datetime
    last_activity: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
