"""
Professional schemas for API serialization.
"""

from pydantic import BaseModel, validator
from typing import Optional, List, Dict
from decimal import Decimal
from datetime import datetime, date, time

from app.schemas.user import UserResponse
from app.schemas.service import ServiceResponse


class ProfessionalBase(BaseModel):
    """Base professional schema."""
    specialties: List[str]
    bio: Optional[str] = None
    experience_years: Optional[int] = None


class ProfessionalCreate(ProfessionalBase):
    """Professional creation schema."""
    user_id: str
    license_number: Optional[str] = None
    portfolio_urls: Optional[List[str]] = None
    certifications: Optional[List[str]] = None
    base_hourly_rate: Optional[Decimal] = None
    commission_percentage: Decimal = 50.00
    instagram_handle: Optional[str] = None
    facebook_url: Optional[str] = None
    website_url: Optional[str] = None
    employment_type: str = "employee"
    preferred_break_duration: int = 30
    max_daily_appointments: Optional[int] = None


class ProfessionalUpdate(BaseModel):
    """Professional update schema."""
    specialties: Optional[List[str]] = None
    bio: Optional[str] = None
    experience_years: Optional[int] = None
    license_number: Optional[str] = None
    portfolio_urls: Optional[List[str]] = None
    certifications: Optional[List[str]] = None
    is_available: Optional[bool] = None
    is_accepting_new_clients: Optional[bool] = None
    base_hourly_rate: Optional[Decimal] = None
    commission_percentage: Optional[Decimal] = None
    instagram_handle: Optional[str] = None
    facebook_url: Optional[str] = None
    website_url: Optional[str] = None
    preferred_break_duration: Optional[int] = None
    max_daily_appointments: Optional[int] = None


class ProfessionalResponse(ProfessionalBase):
    """Professional response schema."""
    id: str
    user_id: str
    full_name: str
    email: str
    phone: Optional[str] = None
    avatar_url: Optional[str] = None
    license_number: Optional[str] = None
    portfolio_urls: Optional[List[str]] = None
    certifications: Optional[List[str]] = None
    is_available: bool
    is_accepting_new_clients: bool
    working_hours: Optional[Dict] = None
    base_hourly_rate: Optional[Decimal] = None
    commission_percentage: Decimal
    total_appointments: int
    completed_appointments: int
    cancelled_appointments: int
    no_show_appointments: int
    rating_average: Optional[Decimal] = None
    rating_count: int
    completion_rate: float
    cancellation_rate: float
    no_show_rate: float
    average_rating_stars: int
    instagram_handle: Optional[str] = None
    facebook_url: Optional[str] = None
    website_url: Optional[str] = None
    hire_date: Optional[datetime] = None
    employment_type: str
    preferred_break_duration: int
    max_daily_appointments: Optional[int] = None
    advance_booking_limit_days: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ProfessionalWithUser(ProfessionalResponse):
    """Professional with user details."""
    user: UserResponse


class ProfessionalList(BaseModel):
    """Professional list response schema."""
    professionals: List[ProfessionalResponse]
    total: int
    page: int
    per_page: int
    pages: int


class ProfessionalSearch(BaseModel):
    """Professional search schema."""
    specialty: Optional[str] = None
    is_available: Optional[bool] = None
    is_accepting_new_clients: Optional[bool] = None
    min_rating: Optional[float] = None
    experience_years: Optional[int] = None


class ProfessionalSchedule(BaseModel):
    """Professional schedule schema."""
    schedule_date: date
    start_time: time
    end_time: time
    schedule_type: str
    title: Optional[str] = None
    description: Optional[str] = None
    is_recurring: bool = False
    recurrence_pattern: Optional[str] = None
    recurrence_end_date: Optional[date] = None


class ProfessionalScheduleCreate(ProfessionalSchedule):
    """Professional schedule creation schema."""
    pass


class ProfessionalScheduleUpdate(BaseModel):
    """Professional schedule update schema."""
    start_time: Optional[time] = None
    end_time: Optional[time] = None
    schedule_type: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    is_recurring: Optional[bool] = None
    recurrence_pattern: Optional[str] = None
    recurrence_end_date: Optional[date] = None


class ProfessionalScheduleResponse(ProfessionalSchedule):
    """Professional schedule response schema."""
    id: str
    professional_id: str
    duration_minutes: int
    is_available_slot: bool
    is_blocked_slot: bool
    is_past: bool
    is_today: bool
    is_future: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ProfessionalAvailability(BaseModel):
    """Professional availability schema."""
    professional_id: str
    date: date
    available_slots: List[Dict]
    working_hours: Optional[Dict] = None
    is_working_day: bool
    total_available_minutes: int


class ProfessionalService(BaseModel):
    """Professional service relationship schema."""
    service_id: str
    custom_price: Optional[Decimal] = None
    custom_duration_minutes: Optional[int] = None
    is_available: bool = True


class ProfessionalServiceResponse(ProfessionalService):
    """Professional service response schema."""
    id: str
    professional_id: str
    effective_price: Decimal
    effective_duration: int
    total_bookings: int
    average_rating: Optional[Decimal] = None
    service: ServiceResponse
    
    class Config:
        from_attributes = True


class ProfessionalStats(BaseModel):
    """Professional statistics schema."""
    total_professionals: int
    active_professionals: int
    available_professionals: int
    specialties: List[str]
    top_rated: List[ProfessionalResponse]
    most_booked: List[ProfessionalResponse]
    performance_metrics: Dict


class ProfessionalWorkingHours(BaseModel):
    """Professional working hours schema."""
    monday: Optional[Dict[str, str]] = None
    tuesday: Optional[Dict[str, str]] = None
    wednesday: Optional[Dict[str, str]] = None
    thursday: Optional[Dict[str, str]] = None
    friday: Optional[Dict[str, str]] = None
    saturday: Optional[Dict[str, str]] = None
    sunday: Optional[Dict[str, str]] = None


class ProfessionalRating(BaseModel):
    """Professional rating schema."""
    rating: int
    comment: Optional[str] = None
    appointment_id: str
    
    @validator('rating')
    def validate_rating(cls, v):
        if not 1 <= v <= 5:
            raise ValueError('Rating must be between 1 and 5')
        return v
