"""
Service schemas for API serialization.
"""

from pydantic import BaseModel, validator
from typing import Optional, List
from decimal import Decimal
from datetime import datetime


class ServiceBase(BaseModel):
    """Base service schema."""
    name: str
    description: Optional[str] = None
    category: str
    price: Decimal
    duration_minutes: int
    
    @validator('price')
    def validate_price(cls, v):
        if v <= 0:
            raise ValueError('Price must be greater than 0')
        return v
    
    @validator('duration_minutes')
    def validate_duration(cls, v):
        if v <= 0:
            raise ValueError('Duration must be greater than 0')
        return v


class ServiceCreate(ServiceBase):
    """Service creation schema."""
    promotional_price: Optional[Decimal] = None
    buffer_time_minutes: int = 15
    is_featured: bool = False
    requires_consultation: bool = False
    min_age: Optional[int] = None
    max_age: Optional[int] = None
    gender_restriction: Optional[str] = None
    preparation_instructions: Optional[str] = None
    aftercare_instructions: Optional[str] = None
    required_skills: Optional[List[str]] = None
    difficulty_level: int = 1
    advance_booking_days: int = 30
    min_advance_hours: int = 2
    cancellation_hours: int = 24
    cancellation_fee_percentage: Decimal = 0
    tags: Optional[List[str]] = None


class ServiceUpdate(BaseModel):
    """Service update schema."""
    name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    price: Optional[Decimal] = None
    promotional_price: Optional[Decimal] = None
    duration_minutes: Optional[int] = None
    buffer_time_minutes: Optional[int] = None
    is_active: Optional[bool] = None
    is_featured: Optional[bool] = None
    requires_consultation: Optional[bool] = None
    image_url: Optional[str] = None
    preparation_instructions: Optional[str] = None
    aftercare_instructions: Optional[str] = None
    required_skills: Optional[List[str]] = None
    difficulty_level: Optional[int] = None
    tags: Optional[List[str]] = None


class ServiceResponse(ServiceBase):
    """Service response schema."""
    id: str
    promotional_price: Optional[Decimal] = None
    current_price: Decimal
    buffer_time_minutes: int
    total_duration_minutes: int
    formatted_duration: str
    is_active: bool
    is_featured: bool
    requires_consultation: bool
    image_url: Optional[str] = None
    gallery_urls: Optional[List[str]] = None
    min_age: Optional[int] = None
    max_age: Optional[int] = None
    gender_restriction: Optional[str] = None
    preparation_instructions: Optional[str] = None
    aftercare_instructions: Optional[str] = None
    required_skills: Optional[List[str]] = None
    difficulty_level: int
    advance_booking_days: int
    min_advance_hours: int
    cancellation_hours: int
    cancellation_fee_percentage: Decimal
    slug: Optional[str] = None
    tags: Optional[List[str]] = None
    booking_count: int
    rating_average: Optional[Decimal] = None
    rating_count: int
    has_promotion: bool
    discount_percentage: float
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ServiceList(BaseModel):
    """Service list response schema."""
    services: List[ServiceResponse]
    total: int
    page: int
    per_page: int
    pages: int


class ServiceCategory(BaseModel):
    """Service category schema."""
    name: str
    count: int
    services: List[ServiceResponse]


class ServiceSearch(BaseModel):
    """Service search schema."""
    query: Optional[str] = None
    category: Optional[str] = None
    min_price: Optional[Decimal] = None
    max_price: Optional[Decimal] = None
    max_duration: Optional[int] = None
    is_featured: Optional[bool] = None
    tags: Optional[List[str]] = None


class ServiceAvailability(BaseModel):
    """Service availability schema."""
    service_id: str
    professional_id: str
    available_slots: List[datetime]
    next_available: Optional[datetime] = None


class ServiceRating(BaseModel):
    """Service rating schema."""
    rating: int
    comment: Optional[str] = None
    
    @validator('rating')
    def validate_rating(cls, v):
        if not 1 <= v <= 5:
            raise ValueError('Rating must be between 1 and 5')
        return v


class ServiceStats(BaseModel):
    """Service statistics schema."""
    total_services: int
    active_services: int
    featured_services: int
    categories: List[str]
    most_booked: List[ServiceResponse]
    highest_rated: List[ServiceResponse]
    revenue_by_service: dict
