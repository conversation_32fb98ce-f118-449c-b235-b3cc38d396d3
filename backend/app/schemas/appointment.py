"""
Appointment schemas for API serialization.
"""

from pydantic import BaseModel, validator
from typing import Optional, List
from decimal import Decimal
from datetime import datetime
from enum import Enum

from app.models.appointment import AppointmentStatus
from app.schemas.user import UserResponse
from app.schemas.service import ServiceResponse


class AppointmentBase(BaseModel):
    """Base appointment schema."""
    professional_id: str
    service_id: str
    appointment_date: datetime
    client_notes: Optional[str] = None


class AppointmentCreate(AppointmentBase):
    """Appointment creation schema."""
    
    @validator('appointment_date')
    def validate_appointment_date(cls, v):
        if v <= datetime.utcnow():
            raise ValueError('Appointment date must be in the future')
        return v


class AppointmentUpdate(BaseModel):
    """Appointment update schema."""
    appointment_date: Optional[datetime] = None
    client_notes: Optional[str] = None
    professional_notes: Optional[str] = None
    internal_notes: Optional[str] = None


class AppointmentReschedule(BaseModel):
    """Appointment reschedule schema."""
    new_appointment_date: datetime
    reason: Optional[str] = None
    
    @validator('new_appointment_date')
    def validate_new_date(cls, v):
        if v <= datetime.utcnow():
            raise ValueError('New appointment date must be in the future')
        return v


class AppointmentCancel(BaseModel):
    """Appointment cancellation schema."""
    reason: Optional[str] = None


class AppointmentConfirm(BaseModel):
    """Appointment confirmation schema."""
    confirmation_method: str = "system"


class AppointmentComplete(BaseModel):
    """Appointment completion schema."""
    actual_duration_minutes: Optional[int] = None
    professional_notes: Optional[str] = None


class AppointmentRating(BaseModel):
    """Appointment rating schema."""
    rating: int
    feedback: Optional[str] = None
    
    @validator('rating')
    def validate_rating(cls, v):
        if not 1 <= v <= 5:
            raise ValueError('Rating must be between 1 and 5')
        return v


class AppointmentResponse(AppointmentBase):
    """Appointment response schema."""
    id: str
    client_id: str
    duration_minutes: int
    end_time: datetime
    status: AppointmentStatus
    service_price: Decimal
    discount_amount: Decimal
    total_price: Decimal
    is_confirmed: bool
    confirmed_at: Optional[datetime] = None
    confirmation_method: Optional[str] = None
    professional_notes: Optional[str] = None
    internal_notes: Optional[str] = None
    cancelled_at: Optional[datetime] = None
    cancelled_by: Optional[str] = None
    cancellation_reason: Optional[str] = None
    cancellation_fee: Decimal
    completed_at: Optional[datetime] = None
    actual_duration_minutes: Optional[int] = None
    client_rating: Optional[int] = None
    client_feedback: Optional[str] = None
    payment_status: str
    payment_method: Optional[str] = None
    paid_amount: Decimal
    remaining_balance: Decimal
    is_paid: bool
    reschedule_count: int
    google_calendar_event_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    # Computed properties
    is_past: bool
    is_today: bool
    is_upcoming: bool
    can_be_cancelled: bool
    can_be_rescheduled: bool
    hours_until_appointment: float
    
    class Config:
        from_attributes = True


class AppointmentWithDetails(AppointmentResponse):
    """Appointment with related details."""
    client: UserResponse
    service: ServiceResponse
    professional: dict  # Professional details


class AppointmentList(BaseModel):
    """Appointment list response schema."""
    appointments: List[AppointmentResponse]
    total: int
    page: int
    per_page: int
    pages: int


class AppointmentSearch(BaseModel):
    """Appointment search schema."""
    client_id: Optional[str] = None
    professional_id: Optional[str] = None
    service_id: Optional[str] = None
    status: Optional[AppointmentStatus] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    is_confirmed: Optional[bool] = None
    payment_status: Optional[str] = None


class AppointmentSlot(BaseModel):
    """Available appointment slot schema."""
    start_time: datetime
    end_time: datetime
    duration_minutes: int
    is_available: bool
    professional_id: str
    service_id: str


class AppointmentAvailability(BaseModel):
    """Appointment availability response schema."""
    date: str
    available_slots: List[AppointmentSlot]
    total_slots: int
    available_count: int


class AppointmentCalendar(BaseModel):
    """Calendar view schema."""
    date: str
    appointments: List[AppointmentResponse]
    available_slots: List[AppointmentSlot]


class AppointmentStats(BaseModel):
    """Appointment statistics schema."""
    total_appointments: int
    scheduled: int
    confirmed: int
    completed: int
    cancelled: int
    no_show: int
    completion_rate: float
    cancellation_rate: float
    average_rating: Optional[float] = None
    revenue_total: Decimal
    revenue_this_month: Decimal


class AppointmentReminder(BaseModel):
    """Appointment reminder schema."""
    appointment_id: str
    reminder_type: str  # "24h", "2h"
    sent_at: datetime
    method: str  # "email", "sms", "whatsapp"


class AppointmentNotification(BaseModel):
    """Appointment notification schema."""
    type: str
    title: str
    message: str
    scheduled_for: Optional[datetime] = None
