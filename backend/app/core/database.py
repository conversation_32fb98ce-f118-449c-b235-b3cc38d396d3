"""
Database configuration and session management.
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import asyncio
from typing import Generator

from app.core.config import settings, get_database_url


# Database URL
DATABASE_URL = get_database_url()

# Create engine
if DATABASE_URL.startswith("sqlite"):
    # SQLite configuration for testing
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=settings.DEBUG
    )
else:
    # PostgreSQL configuration
    engine = create_engine(
        DATABASE_URL,
        pool_pre_ping=True,
        pool_recycle=300,
        pool_size=10,
        max_overflow=20,
        echo=settings.DEBUG
    )

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

# Metadata for migrations
metadata = MetaData()


def get_db() -> Generator[Session, None, None]:
    """
    Get database session.
    
    Yields:
        Session: Database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def create_tables():
    """Create database tables."""
    try:
        # Import all models to ensure they are registered
        from app.models import user, service, professional, appointment, payment, notification
        
        # Create tables
        Base.metadata.create_all(bind=engine)
        print("Database tables created successfully")
    except Exception as e:
        print(f"Error creating database tables: {e}")


async def drop_tables():
    """Drop all database tables."""
    try:
        Base.metadata.drop_all(bind=engine)
        print("Database tables dropped successfully")
    except Exception as e:
        print(f"Error dropping database tables: {e}")


def init_db():
    """Initialize database with default data."""
    from app.models.user import User
    from app.models.service import Service
    from app.models.professional import Professional
    from app.core.security import get_password_hash
    
    db = SessionLocal()
    
    try:
        # Check if admin user exists
        admin_user = db.query(User).filter(User.email == "<EMAIL>").first()
        
        if not admin_user:
            # Create admin user
            admin_user = User(
                email="<EMAIL>",
                full_name="Administrator",
                hashed_password=get_password_hash("admin123"),
                role="admin",
                is_active=True,
                is_verified=True
            )
            db.add(admin_user)
            db.commit()
            print("Admin user created: <EMAIL> / admin123")
        
        # Check if default services exist
        services_count = db.query(Service).count()
        
        if services_count == 0:
            # Create default services
            default_services = [
                {
                    "name": "Corte Feminino",
                    "description": "Corte de cabelo feminino",
                    "duration_minutes": 60,
                    "price": 50.00,
                    "category": "Cabelo"
                },
                {
                    "name": "Corte Masculino",
                    "description": "Corte de cabelo masculino",
                    "duration_minutes": 30,
                    "price": 30.00,
                    "category": "Cabelo"
                },
                {
                    "name": "Coloração",
                    "description": "Coloração completa",
                    "duration_minutes": 120,
                    "price": 100.00,
                    "category": "Cabelo"
                },
                {
                    "name": "Manicure",
                    "description": "Manicure completa",
                    "duration_minutes": 45,
                    "price": 25.00,
                    "category": "Unhas"
                },
                {
                    "name": "Pedicure",
                    "description": "Pedicure completa",
                    "duration_minutes": 60,
                    "price": 35.00,
                    "category": "Unhas"
                },
                {
                    "name": "Limpeza de Pele",
                    "description": "Limpeza facial completa",
                    "duration_minutes": 90,
                    "price": 80.00,
                    "category": "Estética"
                }
            ]
            
            for service_data in default_services:
                service = Service(**service_data)
                db.add(service)
            
            db.commit()
            print(f"Created {len(default_services)} default services")
        
        # Check if default professional exists
        professional_count = db.query(Professional).count()
        
        if professional_count == 0:
            # Create default professional
            professional_user = User(
                email="<EMAIL>",
                full_name="Professional Demo",
                hashed_password=get_password_hash("prof123"),
                role="professional",
                is_active=True,
                is_verified=True
            )
            db.add(professional_user)
            db.flush()  # Get the user ID
            
            professional = Professional(
                user_id=professional_user.id,
                specialties=["Cabelo", "Unhas"],
                bio="Profissional experiente em cortes e cuidados com unhas",
                is_available=True
            )
            db.add(professional)
            db.commit()
            print("Professional user created: <EMAIL> / prof123")
    
    except Exception as e:
        print(f"Error initializing database: {e}")
        db.rollback()
    finally:
        db.close()


# Database health check
def check_database_connection() -> bool:
    """Check if database connection is working."""
    try:
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        return True
    except Exception as e:
        print(f"Database connection failed: {e}")
        return False
