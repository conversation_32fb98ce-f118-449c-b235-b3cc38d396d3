"""
Security utilities for authentication and authorization.
"""

from datetime import datetime, timedelta
from typing import Optional, Union, Any
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import secrets
import string

from app.core.config import settings


# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT token security
security = HTTPBearer()


def create_access_token(
    subject: Union[str, Any],
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create JWT access token.
    
    Args:
        subject: Token subject (usually user ID)
        expires_delta: Token expiration time
        
    Returns:
        str: JWT token
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {"exp": expire, "sub": str(subject), "type": "access"}
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def create_refresh_token(subject: Union[str, Any]) -> str:
    """
    Create JWT refresh token.
    
    Args:
        subject: Token subject (usually user ID)
        
    Returns:
        str: JWT refresh token
    """
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode = {"exp": expire, "sub": str(subject), "type": "refresh"}
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def verify_token(token: str, token_type: str = "access") -> Optional[str]:
    """
    Verify JWT token and return subject.
    
    Args:
        token: JWT token
        token_type: Expected token type
        
    Returns:
        Optional[str]: Token subject if valid, None otherwise
    """
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        # Check token type
        if payload.get("type") != token_type:
            return None
        
        # Get subject
        subject: str = payload.get("sub")
        if subject is None:
            return None
        
        return subject
    except JWTError:
        return None


def get_password_hash(password: str) -> str:
    """
    Hash password.
    
    Args:
        password: Plain password
        
    Returns:
        str: Hashed password
    """
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify password against hash.
    
    Args:
        plain_password: Plain password
        hashed_password: Hashed password
        
    Returns:
        bool: True if password matches
    """
    return pwd_context.verify(plain_password, hashed_password)


def generate_password_reset_token(email: str) -> str:
    """
    Generate password reset token.

    Args:
        email: User email

    Returns:
        str: Reset token
    """
    delta = timedelta(hours=24)  # 24 hours expiration
    now = datetime.utcnow()
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email, "type": "password_reset"},
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM,
    )
    return encoded_jwt


def verify_password_reset_token(token: str) -> Optional[str]:
    """
    Verify password reset token.
    
    Args:
        token: Reset token
        
    Returns:
        Optional[str]: Email if token is valid
    """
    try:
        decoded_token = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        # Check token type
        if decoded_token.get("type") != "password_reset":
            return None
        
        return decoded_token["sub"]
    except JWTError:
        return None


def generate_random_password(length: int = 12) -> str:
    """
    Generate random password.
    
    Args:
        length: Password length
        
    Returns:
        str: Random password
    """
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    password = ''.join(secrets.choice(alphabet) for _ in range(length))
    return password


def generate_verification_token() -> str:
    """
    Generate email verification token.
    
    Returns:
        str: Verification token
    """
    return secrets.token_urlsafe(32)


def generate_api_key() -> str:
    """
    Generate API key.
    
    Returns:
        str: API key
    """
    return secrets.token_urlsafe(32)


# Authentication dependency
async def get_current_user_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """
    Get current user from JWT token.
    
    Args:
        credentials: HTTP authorization credentials
        
    Returns:
        str: User ID
        
    Raises:
        HTTPException: If token is invalid
    """
    token = credentials.credentials
    user_id = verify_token(token, "access")
    
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user_id


# Role-based access control
def require_roles(*allowed_roles: str):
    """
    Decorator to require specific roles.
    
    Args:
        allowed_roles: Allowed user roles
        
    Returns:
        Dependency function
    """
    def role_checker(current_user: dict = Depends(get_current_user_token)):
        user_role = current_user.get("role")
        if user_role not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        return current_user
    
    return role_checker


# Permission checks
def check_permission(user_role: str, required_permission: str) -> bool:
    """
    Check if user role has required permission.
    
    Args:
        user_role: User role
        required_permission: Required permission
        
    Returns:
        bool: True if user has permission
    """
    permissions = {
        "client": [
            "view_own_appointments",
            "create_appointment",
            "cancel_own_appointment",
            "view_services",
            "view_professionals"
        ],
        "professional": [
            "view_own_appointments",
            "view_assigned_appointments",
            "update_appointment_status",
            "view_services",
            "view_own_schedule"
        ],
        "admin": [
            "view_all_appointments",
            "create_appointment",
            "update_appointment",
            "cancel_appointment",
            "manage_services",
            "manage_professionals",
            "view_reports",
            "manage_users"
        ],
        "owner": [
            "*"  # All permissions
        ]
    }
    
    user_permissions = permissions.get(user_role, [])
    
    # Owner has all permissions
    if "*" in user_permissions:
        return True
    
    return required_permission in user_permissions


# Rate limiting decorator
def rate_limit(max_requests: int, window_seconds: int):
    """
    Rate limiting decorator.
    
    Args:
        max_requests: Maximum requests allowed
        window_seconds: Time window in seconds
        
    Returns:
        Decorator function
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Implementation would use Redis for rate limiting
            # This is a placeholder
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# Input sanitization
def sanitize_input(text: str) -> str:
    """
    Sanitize user input.
    
    Args:
        text: Input text
        
    Returns:
        str: Sanitized text
    """
    if not text:
        return ""
    
    # Remove potentially dangerous characters
    dangerous_chars = ["<", ">", "&", "\"", "'", "/", "\\"]
    sanitized = text
    
    for char in dangerous_chars:
        sanitized = sanitized.replace(char, "")
    
    return sanitized.strip()


# CSRF token generation
def generate_csrf_token() -> str:
    """
    Generate CSRF token.
    
    Returns:
        str: CSRF token
    """
    return secrets.token_urlsafe(32)


def verify_csrf_token(token: str, expected_token: str) -> bool:
    """
    Verify CSRF token.
    
    Args:
        token: Provided token
        expected_token: Expected token
        
    Returns:
        bool: True if tokens match
    """
    return secrets.compare_digest(token, expected_token)
