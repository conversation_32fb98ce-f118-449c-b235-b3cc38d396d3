"""
Redis configuration and client.
"""

import redis.asyncio as redis
from typing import Optional, Any, Union
import json
import pickle
from datetime import timedelta

from app.core.config import settings


class RedisClient:
    """Redis client wrapper with common operations."""
    
    def __init__(self, url: str):
        self.url = url
        self._client: Optional[redis.Redis] = None
    
    async def connect(self):
        """Connect to Red<PERSON>."""
        if not self._client:
            self._client = redis.from_url(
                self.url,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
    
    async def disconnect(self):
        """Disconnect from Redis."""
        if self._client:
            await self._client.close()
            self._client = None
    
    async def ping(self) -> bool:
        """Ping Redis server."""
        if not self._client:
            await self.connect()
        return await self._client.ping()
    
    async def get(self, key: str) -> Optional[str]:
        """Get value by key."""
        if not self._client:
            await self.connect()
        return await self._client.get(key)
    
    async def set(
        self,
        key: str,
        value: str,
        expire: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """Set key-value pair with optional expiration."""
        if not self._client:
            await self.connect()
        return await self._client.set(key, value, ex=expire)
    
    async def delete(self, *keys: str) -> int:
        """Delete keys."""
        if not self._client:
            await self.connect()
        return await self._client.delete(*keys)
    
    async def exists(self, key: str) -> bool:
        """Check if key exists."""
        if not self._client:
            await self.connect()
        return bool(await self._client.exists(key))
    
    async def expire(self, key: str, seconds: int) -> bool:
        """Set expiration for key."""
        if not self._client:
            await self.connect()
        return await self._client.expire(key, seconds)
    
    async def ttl(self, key: str) -> int:
        """Get time to live for key."""
        if not self._client:
            await self.connect()
        return await self._client.ttl(key)
    
    async def incr(self, key: str, amount: int = 1) -> int:
        """Increment key value."""
        if not self._client:
            await self.connect()
        return await self._client.incr(key, amount)
    
    async def decr(self, key: str, amount: int = 1) -> int:
        """Decrement key value."""
        if not self._client:
            await self.connect()
        return await self._client.decr(key, amount)
    
    # JSON operations
    async def set_json(
        self,
        key: str,
        value: Any,
        expire: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """Set JSON value."""
        json_value = json.dumps(value, default=str)
        return await self.set(key, json_value, expire)
    
    async def get_json(self, key: str) -> Optional[Any]:
        """Get JSON value."""
        value = await self.get(key)
        if value:
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return None
        return None
    
    # Hash operations
    async def hset(self, name: str, key: str, value: str) -> int:
        """Set hash field."""
        if not self._client:
            await self.connect()
        return await self._client.hset(name, key, value)
    
    async def hget(self, name: str, key: str) -> Optional[str]:
        """Get hash field."""
        if not self._client:
            await self.connect()
        return await self._client.hget(name, key)
    
    async def hgetall(self, name: str) -> dict:
        """Get all hash fields."""
        if not self._client:
            await self.connect()
        return await self._client.hgetall(name)
    
    async def hdel(self, name: str, *keys: str) -> int:
        """Delete hash fields."""
        if not self._client:
            await self.connect()
        return await self._client.hdel(name, *keys)
    
    # List operations
    async def lpush(self, name: str, *values: str) -> int:
        """Push values to left of list."""
        if not self._client:
            await self.connect()
        return await self._client.lpush(name, *values)
    
    async def rpush(self, name: str, *values: str) -> int:
        """Push values to right of list."""
        if not self._client:
            await self.connect()
        return await self._client.rpush(name, *values)
    
    async def lpop(self, name: str) -> Optional[str]:
        """Pop value from left of list."""
        if not self._client:
            await self.connect()
        return await self._client.lpop(name)
    
    async def rpop(self, name: str) -> Optional[str]:
        """Pop value from right of list."""
        if not self._client:
            await self.connect()
        return await self._client.rpop(name)
    
    async def lrange(self, name: str, start: int, end: int) -> list:
        """Get range of list values."""
        if not self._client:
            await self.connect()
        return await self._client.lrange(name, start, end)
    
    # Set operations
    async def sadd(self, name: str, *values: str) -> int:
        """Add values to set."""
        if not self._client:
            await self.connect()
        return await self._client.sadd(name, *values)
    
    async def srem(self, name: str, *values: str) -> int:
        """Remove values from set."""
        if not self._client:
            await self.connect()
        return await self._client.srem(name, *values)
    
    async def smembers(self, name: str) -> set:
        """Get all set members."""
        if not self._client:
            await self.connect()
        return await self._client.smembers(name)
    
    async def sismember(self, name: str, value: str) -> bool:
        """Check if value is in set."""
        if not self._client:
            await self.connect()
        return await self._client.sismember(name, value)
    
    # Cache operations with automatic serialization
    async def cache_set(
        self,
        key: str,
        value: Any,
        expire: int = settings.CACHE_TTL_SECONDS
    ) -> bool:
        """Cache a value with automatic serialization."""
        try:
            if isinstance(value, (dict, list, tuple)):
                serialized_value = json.dumps(value, default=str)
            else:
                serialized_value = str(value)
            
            return await self.set(f"cache:{key}", serialized_value, expire)
        except Exception:
            return False
    
    async def cache_get(self, key: str) -> Optional[Any]:
        """Get cached value with automatic deserialization."""
        try:
            value = await self.get(f"cache:{key}")
            if value:
                try:
                    return json.loads(value)
                except json.JSONDecodeError:
                    return value
            return None
        except Exception:
            return None
    
    async def cache_delete(self, key: str) -> bool:
        """Delete cached value."""
        try:
            result = await self.delete(f"cache:{key}")
            return result > 0
        except Exception:
            return False
    
    async def close(self):
        """Close Redis connection."""
        await self.disconnect()


# Create Redis client instance
redis_client = RedisClient(settings.REDIS_URL)


# Cache decorator
def cache_result(key_prefix: str, expire: int = settings.CACHE_TTL_SECONDS):
    """Decorator to cache function results."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"
            
            # Try to get from cache
            cached_result = await redis_client.cache_get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await redis_client.cache_set(cache_key, result, expire)
            
            return result
        return wrapper
    return decorator


# Rate limiting
async def is_rate_limited(identifier: str, limit: int, window: int) -> bool:
    """Check if identifier is rate limited."""
    key = f"rate_limit:{identifier}"
    current = await redis_client.get(key)
    
    if current is None:
        await redis_client.set(key, "1", window)
        return False
    
    if int(current) >= limit:
        return True
    
    await redis_client.incr(key)
    return False


# Session management
async def create_session(user_id: str, session_data: dict) -> str:
    """Create user session."""
    import uuid
    session_id = str(uuid.uuid4())
    session_key = f"session:{session_id}"
    
    await redis_client.set_json(
        session_key,
        {"user_id": user_id, **session_data},
        expire=settings.SESSION_TIMEOUT_MINUTES * 60
    )
    
    return session_id


async def get_session(session_id: str) -> Optional[dict]:
    """Get session data."""
    session_key = f"session:{session_id}"
    return await redis_client.get_json(session_key)


async def delete_session(session_id: str) -> bool:
    """Delete session."""
    session_key = f"session:{session_id}"
    result = await redis_client.delete(session_key)
    return result > 0
