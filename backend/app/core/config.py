"""
Application configuration settings.
"""

from typing import List, Optional, Union
from pydantic import BaseSettings, validator, AnyHttpUrl
import secrets
import os


class Settings(BaseSettings):
    """Application settings."""
    
    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Salon Booking System"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # CORS Configuration
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = [
        "http://localhost:3000",
        "http://localhost:8080",
        "http://localhost:5173"
    ]
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # Database Configuration
    DATABASE_URL: str = "postgresql://salon_user:salon_password@localhost:5432/salon_db"
    
    # Redis Configuration
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Email Configuration (SendGrid)
    SENDGRID_API_KEY: Optional[str] = None
    FROM_EMAIL: str = "<EMAIL>"
    FROM_NAME: str = "Your Salon Name"
    
    # SMS Configuration (Twilio)
    TWILIO_ACCOUNT_SID: Optional[str] = None
    TWILIO_AUTH_TOKEN: Optional[str] = None
    TWILIO_PHONE_NUMBER: Optional[str] = None
    
    # WhatsApp Configuration
    WHATSAPP_TOKEN: Optional[str] = None
    WHATSAPP_PHONE_NUMBER_ID: Optional[str] = None
    
    # Payment Configuration
    # Mercado Pago
    MERCADOPAGO_ACCESS_TOKEN: Optional[str] = None
    MERCADOPAGO_PUBLIC_KEY: Optional[str] = None
    
    # Stripe
    STRIPE_SECRET_KEY: Optional[str] = None
    STRIPE_PUBLISHABLE_KEY: Optional[str] = None
    STRIPE_WEBHOOK_SECRET: Optional[str] = None
    
    # PIX Configuration
    PIX_KEY: Optional[str] = None
    PIX_BANK_CODE: Optional[str] = None
    
    # Google Calendar Configuration
    GOOGLE_CLIENT_ID: Optional[str] = None
    GOOGLE_CLIENT_SECRET: Optional[str] = None
    GOOGLE_REDIRECT_URI: str = "http://localhost:8000/auth/google/callback"
    
    # File Upload Configuration
    MAX_FILE_SIZE: int = 5242880  # 5MB
    UPLOAD_FOLDER: str = "uploads/"
    ALLOWED_EXTENSIONS: List[str] = ["jpg", "jpeg", "png", "gif"]
    
    # Application Configuration
    DEBUG: bool = True
    ENVIRONMENT: str = "development"
    LOG_LEVEL: str = "INFO"
    
    # Security Configuration
    BCRYPT_ROUNDS: int = 12
    SESSION_TIMEOUT_MINUTES: int = 60
    MAX_LOGIN_ATTEMPTS: int = 5
    LOCKOUT_DURATION_MINUTES: int = 15
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_BURST: int = 10
    
    # Notification Settings
    APPOINTMENT_REMINDER_HOURS: int = 24
    APPOINTMENT_CONFIRMATION_REQUIRED: bool = True
    SMS_NOTIFICATIONS_ENABLED: bool = True
    EMAIL_NOTIFICATIONS_ENABLED: bool = True
    WHATSAPP_NOTIFICATIONS_ENABLED: bool = True
    
    # Business Configuration
    BUSINESS_NAME: str = "Your Salon Name"
    BUSINESS_ADDRESS: str = "Your Salon Address"
    BUSINESS_PHONE: str = "+1234567890"
    BUSINESS_EMAIL: str = "<EMAIL>"
    BUSINESS_TIMEZONE: str = "America/Sao_Paulo"
    
    # Working Hours (24h format)
    BUSINESS_HOURS_START: str = "09:00"
    BUSINESS_HOURS_END: str = "18:00"
    BUSINESS_DAYS: str = "1,2,3,4,5,6"  # Monday=1, Sunday=7
    
    @validator("BUSINESS_DAYS", pre=True)
    def parse_business_days(cls, v: str) -> List[int]:
        if isinstance(v, str):
            return [int(day.strip()) for day in v.split(",")]
        return v
    
    # Appointment Configuration
    DEFAULT_APPOINTMENT_DURATION: int = 60  # minutes
    APPOINTMENT_BUFFER_TIME: int = 15  # minutes between appointments
    MAX_ADVANCE_BOOKING_DAYS: int = 30
    MIN_ADVANCE_BOOKING_HOURS: int = 2
    
    # Cache Configuration
    CACHE_TTL_SECONDS: int = 300
    CACHE_MAX_SIZE: int = 1000
    
    # Testing
    TESTING: bool = False
    
    @validator("DATABASE_URL", pre=True)
    def validate_database_url(cls, v: str) -> str:
        if v.startswith("sqlite"):
            # For testing with SQLite
            return v
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()


# Database URL for SQLAlchemy
def get_database_url() -> str:
    """Get database URL for SQLAlchemy."""
    if settings.TESTING:
        return "sqlite:///./test.db"
    return settings.DATABASE_URL


# Check if all required settings are configured
def check_required_settings() -> List[str]:
    """Check if all required settings are configured."""
    missing = []
    
    # Check database
    if not settings.DATABASE_URL:
        missing.append("DATABASE_URL")
    
    # Check Redis
    if not settings.REDIS_URL:
        missing.append("REDIS_URL")
    
    # Check secret key
    if not settings.SECRET_KEY:
        missing.append("SECRET_KEY")
    
    return missing


# Get upload path
def get_upload_path() -> str:
    """Get upload directory path."""
    upload_path = os.path.join(os.getcwd(), settings.UPLOAD_FOLDER)
    os.makedirs(upload_path, exist_ok=True)
    return upload_path
