"""
Custom exceptions for the salon booking system.
"""

from typing import Optional, Dict, Any
from fastapi import HTTPException, status


class SalonException(Exception):
    """Base exception for salon booking system."""
    
    def __init__(
        self,
        message: str,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        error_code: str = "SALON_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class AuthenticationError(SalonException):
    """Authentication related errors."""
    
    def __init__(self, message: str = "Authentication failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            error_code="AUTHENTICATION_ERROR",
            details=details
        )


class AuthorizationError(SalonException):
    """Authorization related errors."""
    
    def __init__(self, message: str = "Insufficient permissions", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN,
            error_code="AUTHORIZATION_ERROR",
            details=details
        )


class ValidationError(SalonException):
    """Data validation errors."""
    
    def __init__(self, message: str = "Validation failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            error_code="VALIDATION_ERROR",
            details=details
        )


class NotFoundError(SalonException):
    """Resource not found errors."""
    
    def __init__(self, message: str = "Resource not found", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            error_code="NOT_FOUND_ERROR",
            details=details
        )


class ConflictError(SalonException):
    """Resource conflict errors."""
    
    def __init__(self, message: str = "Resource conflict", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            error_code="CONFLICT_ERROR",
            details=details
        )


class BusinessRuleError(SalonException):
    """Business rule violation errors."""
    
    def __init__(self, message: str = "Business rule violation", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code="BUSINESS_RULE_ERROR",
            details=details
        )


class AppointmentError(SalonException):
    """Appointment related errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code="APPOINTMENT_ERROR",
            details=details
        )


class PaymentError(SalonException):
    """Payment related errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code="PAYMENT_ERROR",
            details=details
        )


class NotificationError(SalonException):
    """Notification related errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code="NOTIFICATION_ERROR",
            details=details
        )


class ExternalServiceError(SalonException):
    """External service integration errors."""
    
    def __init__(self, message: str, service: str, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["service"] = service
        super().__init__(
            message=message,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            error_code="EXTERNAL_SERVICE_ERROR",
            details=details
        )


class RateLimitError(SalonException):
    """Rate limiting errors."""
    
    def __init__(self, message: str = "Rate limit exceeded", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            error_code="RATE_LIMIT_ERROR",
            details=details
        )


class FileUploadError(SalonException):
    """File upload related errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code="FILE_UPLOAD_ERROR",
            details=details
        )


# Specific appointment errors
class AppointmentNotAvailableError(AppointmentError):
    """Appointment slot not available."""
    
    def __init__(self, datetime_str: str):
        super().__init__(
            message=f"Horário {datetime_str} não está disponível",
            details={"requested_datetime": datetime_str}
        )


class AppointmentTooEarlyError(AppointmentError):
    """Appointment scheduled too early."""
    
    def __init__(self, min_hours: int):
        super().__init__(
            message=f"Agendamento deve ser feito com pelo menos {min_hours} horas de antecedência",
            details={"minimum_hours": min_hours}
        )


class AppointmentTooLateError(AppointmentError):
    """Appointment scheduled too far in advance."""
    
    def __init__(self, max_days: int):
        super().__init__(
            message=f"Agendamento não pode ser feito com mais de {max_days} dias de antecedência",
            details={"maximum_days": max_days}
        )


class AppointmentOutsideBusinessHoursError(AppointmentError):
    """Appointment outside business hours."""
    
    def __init__(self, business_hours: str):
        super().__init__(
            message=f"Agendamento fora do horário de funcionamento: {business_hours}",
            details={"business_hours": business_hours}
        )


class ProfessionalNotAvailableError(AppointmentError):
    """Professional not available for appointment."""
    
    def __init__(self, professional_name: str, datetime_str: str):
        super().__init__(
            message=f"Profissional {professional_name} não está disponível em {datetime_str}",
            details={
                "professional": professional_name,
                "requested_datetime": datetime_str
            }
        )


# Specific payment errors
class PaymentProcessingError(PaymentError):
    """Payment processing failed."""
    
    def __init__(self, provider: str, error_message: str):
        super().__init__(
            message=f"Erro no processamento do pagamento via {provider}: {error_message}",
            details={
                "provider": provider,
                "error_message": error_message
            }
        )


class PaymentNotFoundError(PaymentError):
    """Payment not found."""
    
    def __init__(self, payment_id: str):
        super().__init__(
            message=f"Pagamento {payment_id} não encontrado",
            details={"payment_id": payment_id}
        )


class InsufficientFundsError(PaymentError):
    """Insufficient funds for payment."""
    
    def __init__(self):
        super().__init__(
            message="Saldo insuficiente para realizar o pagamento"
        )


# Specific user errors
class UserNotFoundError(NotFoundError):
    """User not found."""
    
    def __init__(self, identifier: str):
        super().__init__(
            message=f"Usuário não encontrado: {identifier}",
            details={"identifier": identifier}
        )


class UserAlreadyExistsError(ConflictError):
    """User already exists."""
    
    def __init__(self, email: str):
        super().__init__(
            message=f"Usuário com email {email} já existe",
            details={"email": email}
        )


class InvalidCredentialsError(AuthenticationError):
    """Invalid login credentials."""
    
    def __init__(self):
        super().__init__(
            message="Email ou senha inválidos"
        )


class AccountNotVerifiedError(AuthenticationError):
    """Account not verified."""
    
    def __init__(self):
        super().__init__(
            message="Conta não verificada. Verifique seu email."
        )


class AccountDisabledError(AuthenticationError):
    """Account disabled."""
    
    def __init__(self):
        super().__init__(
            message="Conta desabilitada. Entre em contato com o suporte."
        )
