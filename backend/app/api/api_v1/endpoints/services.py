"""
Service management endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from typing import List, Optional

from app.core.database import get_db
from app.core.security import get_current_user_token, check_permission
from app.models.user import User
from app.models.service import Service
from app.schemas.service import (
    ServiceCreate,
    ServiceUpdate,
    ServiceResponse,
    ServiceList,
    ServiceSearch,
    ServiceStats,
    ServiceCategory
)
from app.core.exceptions import (
    NotFoundError,
    AuthorizationError
)

router = APIRouter()


async def get_current_user(
    current_user_id: str = Depends(get_current_user_token),
    db: Session = Depends(get_db)
) -> User:
    """Get current authenticated user."""
    user = db.query(User).filter(User.id == current_user_id).first()
    if not user:
        raise NotFoundError("User not found")
    return user


@router.get("/", response_model=ServiceList)
async def get_services(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    category: Optional[str] = None,
    is_active: Optional[bool] = True,
    is_featured: Optional[bool] = None,
    search: Optional[str] = None,
    min_price: Optional[float] = None,
    max_price: Optional[float] = None,
    max_duration: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """
    Get list of services with filtering and pagination.
    """
    # Build query
    query = db.query(Service).filter(Service.deleted_at.is_(None))
    
    # Apply filters
    if is_active is not None:
        query = query.filter(Service.is_active == is_active)
    
    if is_featured is not None:
        query = query.filter(Service.is_featured == is_featured)
    
    if category:
        query = query.filter(Service.category.ilike(f"%{category}%"))
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            (Service.name.ilike(search_term)) |
            (Service.description.ilike(search_term)) |
            (Service.category.ilike(search_term))
        )
    
    if min_price is not None:
        query = query.filter(Service.price >= min_price)
    
    if max_price is not None:
        query = query.filter(Service.price <= max_price)
    
    if max_duration is not None:
        query = query.filter(Service.duration_minutes <= max_duration)
    
    # Order by featured first, then by name
    query = query.order_by(desc(Service.is_featured), Service.name)
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    offset = (page - 1) * per_page
    services = query.offset(offset).limit(per_page).all()
    
    # Calculate pages
    pages = (total + per_page - 1) // per_page
    
    return ServiceList(
        services=[ServiceResponse.from_orm(service) for service in services],
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.get("/categories", response_model=List[ServiceCategory])
async def get_service_categories(
    db: Session = Depends(get_db)
):
    """
    Get all service categories with counts.
    """
    # Get categories with service counts
    categories = db.query(
        Service.category,
        func.count(Service.id).label('count')
    ).filter(
        Service.deleted_at.is_(None),
        Service.is_active == True
    ).group_by(Service.category).all()
    
    result = []
    for category, count in categories:
        # Get services for this category
        services = db.query(Service).filter(
            Service.category == category,
            Service.deleted_at.is_(None),
            Service.is_active == True
        ).limit(5).all()  # Limit to 5 services per category
        
        result.append(ServiceCategory(
            name=category,
            count=count,
            services=[ServiceResponse.from_orm(service) for service in services]
        ))
    
    return result


@router.get("/featured", response_model=List[ServiceResponse])
async def get_featured_services(
    limit: int = Query(6, ge=1, le=20),
    db: Session = Depends(get_db)
):
    """
    Get featured services.
    """
    services = db.query(Service).filter(
        Service.deleted_at.is_(None),
        Service.is_active == True,
        Service.is_featured == True
    ).order_by(Service.name).limit(limit).all()
    
    return [ServiceResponse.from_orm(service) for service in services]


@router.get("/{service_id}", response_model=ServiceResponse)
async def get_service(
    service_id: str,
    db: Session = Depends(get_db)
):
    """
    Get service by ID.
    """
    service = db.query(Service).filter(
        Service.id == service_id,
        Service.deleted_at.is_(None)
    ).first()
    
    if not service:
        raise NotFoundError("Service not found")
    
    return ServiceResponse.from_orm(service)


@router.post("/", response_model=ServiceResponse)
async def create_service(
    service_data: ServiceCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create new service (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "manage_services"):
        raise AuthorizationError()
    
    # Create service
    service = Service(**service_data.dict())
    service.generate_slug()
    
    db.add(service)
    db.commit()
    db.refresh(service)
    
    return ServiceResponse.from_orm(service)


@router.put("/{service_id}", response_model=ServiceResponse)
async def update_service(
    service_id: str,
    service_update: ServiceUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update service by ID (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "manage_services"):
        raise AuthorizationError()
    
    service = db.query(Service).filter(
        Service.id == service_id,
        Service.deleted_at.is_(None)
    ).first()
    
    if not service:
        raise NotFoundError("Service not found")
    
    # Update service fields
    update_data = service_update.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        if hasattr(service, field):
            setattr(service, field, value)
    
    # Regenerate slug if name changed
    if 'name' in update_data:
        service.generate_slug()
    
    db.commit()
    db.refresh(service)
    
    return ServiceResponse.from_orm(service)


@router.delete("/{service_id}")
async def delete_service(
    service_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Soft delete service by ID (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "manage_services"):
        raise AuthorizationError()
    
    service = db.query(Service).filter(
        Service.id == service_id,
        Service.deleted_at.is_(None)
    ).first()
    
    if not service:
        raise NotFoundError("Service not found")
    
    # Check if service has active appointments
    from app.models.appointment import Appointment, AppointmentStatus
    active_appointments = db.query(Appointment).filter(
        Appointment.service_id == service_id,
        Appointment.status.in_([
            AppointmentStatus.SCHEDULED,
            AppointmentStatus.CONFIRMED,
            AppointmentStatus.IN_PROGRESS
        ])
    ).count()
    
    if active_appointments > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot delete service with {active_appointments} active appointments"
        )
    
    # Soft delete
    service.soft_delete()
    db.commit()
    
    return {"message": "Service deleted successfully"}


@router.post("/{service_id}/activate")
async def activate_service(
    service_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Activate service (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "manage_services"):
        raise AuthorizationError()
    
    service = db.query(Service).filter(Service.id == service_id).first()
    
    if not service:
        raise NotFoundError("Service not found")
    
    service.is_active = True
    service.restore()  # Remove soft delete if applicable
    db.commit()
    
    return {"message": "Service activated successfully"}


@router.post("/{service_id}/deactivate")
async def deactivate_service(
    service_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Deactivate service (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "manage_services"):
        raise AuthorizationError()
    
    service = db.query(Service).filter(Service.id == service_id).first()
    
    if not service:
        raise NotFoundError("Service not found")
    
    service.is_active = False
    db.commit()
    
    return {"message": "Service deactivated successfully"}


@router.get("/stats/overview", response_model=ServiceStats)
async def get_service_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get service statistics (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "view_reports"):
        raise AuthorizationError()
    
    # Get service counts
    total_services = db.query(Service).filter(Service.deleted_at.is_(None)).count()
    active_services = db.query(Service).filter(
        Service.deleted_at.is_(None),
        Service.is_active == True
    ).count()
    featured_services = db.query(Service).filter(
        Service.deleted_at.is_(None),
        Service.is_featured == True
    ).count()
    
    # Get categories
    categories = db.query(Service.category).filter(
        Service.deleted_at.is_(None)
    ).distinct().all()
    categories = [cat[0] for cat in categories]
    
    # Get most booked services
    most_booked = db.query(Service).filter(
        Service.deleted_at.is_(None),
        Service.is_active == True
    ).order_by(desc(Service.booking_count)).limit(5).all()
    
    # Get highest rated services
    highest_rated = db.query(Service).filter(
        Service.deleted_at.is_(None),
        Service.is_active == True,
        Service.rating_average.isnot(None)
    ).order_by(desc(Service.rating_average)).limit(5).all()
    
    # Calculate revenue by service (would need appointment data)
    revenue_by_service = {}  # TODO: Implement with appointment data
    
    return ServiceStats(
        total_services=total_services,
        active_services=active_services,
        featured_services=featured_services,
        categories=categories,
        most_booked=[ServiceResponse.from_orm(service) for service in most_booked],
        highest_rated=[ServiceResponse.from_orm(service) for service in highest_rated],
        revenue_by_service=revenue_by_service
    )
