"""
Professional management endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import List, Optional
from datetime import date, datetime, timedelta

from app.core.database import get_db
from app.core.security import get_current_user_token, check_permission
from app.models.user import User
from app.models.professional import Professional
from app.models.professional_schedule import ProfessionalSchedule, ScheduleType
from app.schemas.professional import (
    ProfessionalCreate,
    ProfessionalUpdate,
    ProfessionalResponse,
    ProfessionalList,
    ProfessionalSearch,
    ProfessionalScheduleCreate,
    ProfessionalScheduleResponse,
    ProfessionalAvailability,
    ProfessionalStats,
    ProfessionalWorkingHours
)
from app.core.exceptions import (
    NotFoundError,
    AuthorizationError,
    ValidationError
)

router = APIRouter()


async def get_current_user(
    current_user_id: str = Depends(get_current_user_token),
    db: Session = Depends(get_db)
) -> User:
    """Get current authenticated user."""
    user = db.query(User).filter(User.id == current_user_id).first()
    if not user:
        raise NotFoundError("User not found")
    return user


@router.get("/", response_model=ProfessionalList)
async def get_professionals(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    specialty: Optional[str] = None,
    is_available: Optional[bool] = True,
    is_accepting_new_clients: Optional[bool] = None,
    min_rating: Optional[float] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Get list of professionals with filtering and pagination.
    """
    # Build query
    query = db.query(Professional).join(User).filter(
        Professional.deleted_at.is_(None),
        User.is_active == True
    )
    
    # Apply filters
    if is_available is not None:
        query = query.filter(Professional.is_available == is_available)
    
    if is_accepting_new_clients is not None:
        query = query.filter(Professional.is_accepting_new_clients == is_accepting_new_clients)
    
    if specialty:
        query = query.filter(Professional.specialties.contains([specialty]))
    
    if min_rating is not None:
        query = query.filter(Professional.rating_average >= min_rating)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            (User.full_name.ilike(search_term)) |
            (Professional.bio.ilike(search_term)) |
            (Professional.specialties.op('&&')(f'{{{search_term}}}'))
        )
    
    # Order by rating and availability
    query = query.order_by(
        desc(Professional.is_available),
        desc(Professional.rating_average),
        User.full_name
    )
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    offset = (page - 1) * per_page
    professionals = query.offset(offset).limit(per_page).all()
    
    # Calculate pages
    pages = (total + per_page - 1) // per_page
    
    return ProfessionalList(
        professionals=[ProfessionalResponse.from_orm(prof) for prof in professionals],
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.get("/{professional_id}", response_model=ProfessionalResponse)
async def get_professional(
    professional_id: str,
    db: Session = Depends(get_db)
):
    """
    Get professional by ID.
    """
    professional = db.query(Professional).filter(
        Professional.id == professional_id,
        Professional.deleted_at.is_(None)
    ).first()
    
    if not professional:
        raise NotFoundError("Professional not found")
    
    return ProfessionalResponse.from_orm(professional)


@router.post("/", response_model=ProfessionalResponse)
async def create_professional(
    professional_data: ProfessionalCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create new professional profile (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "manage_professionals"):
        raise AuthorizationError()
    
    # Check if user exists and doesn't already have a professional profile
    user = db.query(User).filter(User.id == professional_data.user_id).first()
    if not user:
        raise NotFoundError("User not found")
    
    existing_professional = db.query(Professional).filter(
        Professional.user_id == professional_data.user_id
    ).first()
    if existing_professional:
        raise ValidationError("User already has a professional profile")
    
    # Create professional profile
    professional = Professional(**professional_data.dict())
    professional.set_default_working_hours()
    
    # Update user role to professional
    user.role = "professional"
    
    db.add(professional)
    db.commit()
    db.refresh(professional)
    
    return ProfessionalResponse.from_orm(professional)


@router.put("/{professional_id}", response_model=ProfessionalResponse)
async def update_professional(
    professional_id: str,
    professional_update: ProfessionalUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update professional profile.
    """
    professional = db.query(Professional).filter(
        Professional.id == professional_id,
        Professional.deleted_at.is_(None)
    ).first()
    
    if not professional:
        raise NotFoundError("Professional not found")
    
    # Check permissions (professional can update own profile, admin can update any)
    if (str(professional.user_id) != str(current_user.id) and 
        not check_permission(current_user.role, "manage_professionals")):
        raise AuthorizationError()
    
    # Update professional fields
    update_data = professional_update.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        if hasattr(professional, field):
            setattr(professional, field, value)
    
    db.commit()
    db.refresh(professional)
    
    return ProfessionalResponse.from_orm(professional)


@router.get("/{professional_id}/availability", response_model=List[ProfessionalAvailability])
async def get_professional_availability(
    professional_id: str,
    start_date: date = Query(...),
    end_date: date = Query(...),
    service_duration: Optional[int] = Query(60),
    db: Session = Depends(get_db)
):
    """
    Get professional availability for date range.
    """
    professional = db.query(Professional).filter(
        Professional.id == professional_id,
        Professional.deleted_at.is_(None)
    ).first()
    
    if not professional:
        raise NotFoundError("Professional not found")
    
    if not professional.is_available:
        return []
    
    # Validate date range
    if end_date < start_date:
        raise ValidationError("End date must be after start date")
    
    if (end_date - start_date).days > 30:
        raise ValidationError("Date range cannot exceed 30 days")
    
    availability = []
    current_date = start_date
    
    while current_date <= end_date:
        # Get working hours for this day
        day_of_week = current_date.weekday()  # 0=Monday, 6=Sunday
        working_hours = professional.get_working_hours(day_of_week)
        
        if working_hours and professional.is_working_on_day(day_of_week):
            # Get existing appointments for this date
            from app.models.appointment import Appointment, AppointmentStatus
            appointments = db.query(Appointment).filter(
                Appointment.professional_id == professional_id,
                Appointment.appointment_date >= datetime.combine(current_date, datetime.min.time()),
                Appointment.appointment_date < datetime.combine(current_date + timedelta(days=1), datetime.min.time()),
                Appointment.status.in_([
                    AppointmentStatus.SCHEDULED,
                    AppointmentStatus.CONFIRMED,
                    AppointmentStatus.IN_PROGRESS
                ])
            ).all()
            
            # Get schedule blocks for this date
            schedule_blocks = db.query(ProfessionalSchedule).filter(
                ProfessionalSchedule.professional_id == professional_id,
                ProfessionalSchedule.schedule_date == current_date
            ).all()
            
            # Calculate available slots
            available_slots = []
            # TODO: Implement slot calculation logic
            
            availability.append(ProfessionalAvailability(
                professional_id=professional_id,
                date=current_date,
                available_slots=available_slots,
                working_hours=working_hours,
                is_working_day=True,
                total_available_minutes=0  # TODO: Calculate
            ))
        else:
            availability.append(ProfessionalAvailability(
                professional_id=professional_id,
                date=current_date,
                available_slots=[],
                working_hours=None,
                is_working_day=False,
                total_available_minutes=0
            ))
        
        current_date += timedelta(days=1)
    
    return availability


@router.put("/{professional_id}/working-hours", response_model=ProfessionalResponse)
async def update_working_hours(
    professional_id: str,
    working_hours: ProfessionalWorkingHours,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update professional working hours.
    """
    professional = db.query(Professional).filter(
        Professional.id == professional_id,
        Professional.deleted_at.is_(None)
    ).first()
    
    if not professional:
        raise NotFoundError("Professional not found")
    
    # Check permissions
    if (str(professional.user_id) != str(current_user.id) and 
        not check_permission(current_user.role, "manage_professionals")):
        raise AuthorizationError()
    
    # Update working hours
    professional.working_hours = working_hours.dict(exclude_unset=True)
    
    db.commit()
    db.refresh(professional)
    
    return ProfessionalResponse.from_orm(professional)


@router.post("/{professional_id}/schedule", response_model=ProfessionalScheduleResponse)
async def create_schedule_block(
    professional_id: str,
    schedule_data: ProfessionalScheduleCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create schedule block for professional.
    """
    professional = db.query(Professional).filter(
        Professional.id == professional_id,
        Professional.deleted_at.is_(None)
    ).first()
    
    if not professional:
        raise NotFoundError("Professional not found")
    
    # Check permissions
    if (str(professional.user_id) != str(current_user.id) and 
        not check_permission(current_user.role, "manage_professionals")):
        raise AuthorizationError()
    
    # Create schedule block
    schedule = ProfessionalSchedule(
        professional_id=professional_id,
        **schedule_data.dict()
    )
    
    db.add(schedule)
    db.commit()
    db.refresh(schedule)
    
    return ProfessionalScheduleResponse.from_orm(schedule)


@router.get("/stats/overview", response_model=ProfessionalStats)
async def get_professional_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get professional statistics (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "view_reports"):
        raise AuthorizationError()
    
    # Get professional counts
    total_professionals = db.query(Professional).filter(
        Professional.deleted_at.is_(None)
    ).count()
    
    active_professionals = db.query(Professional).join(User).filter(
        Professional.deleted_at.is_(None),
        User.is_active == True,
        Professional.is_available == True
    ).count()
    
    available_professionals = db.query(Professional).filter(
        Professional.deleted_at.is_(None),
        Professional.is_available == True,
        Professional.is_accepting_new_clients == True
    ).count()
    
    # Get all specialties
    specialties_query = db.query(Professional.specialties).filter(
        Professional.deleted_at.is_(None)
    ).all()
    
    all_specialties = set()
    for spec_list in specialties_query:
        if spec_list[0]:  # Check if not None
            all_specialties.update(spec_list[0])
    
    specialties = list(all_specialties)
    
    # Get top rated professionals
    top_rated = db.query(Professional).filter(
        Professional.deleted_at.is_(None),
        Professional.rating_average.isnot(None)
    ).order_by(desc(Professional.rating_average)).limit(5).all()
    
    # Get most booked professionals
    most_booked = db.query(Professional).filter(
        Professional.deleted_at.is_(None)
    ).order_by(desc(Professional.total_appointments)).limit(5).all()
    
    return ProfessionalStats(
        total_professionals=total_professionals,
        active_professionals=active_professionals,
        available_professionals=available_professionals,
        specialties=specialties,
        top_rated=[ProfessionalResponse.from_orm(prof) for prof in top_rated],
        most_booked=[ProfessionalResponse.from_orm(prof) for prof in most_booked],
        performance_metrics={}  # TODO: Add detailed metrics
    )
