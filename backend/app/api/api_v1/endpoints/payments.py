"""
Payment management endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional

from app.core.database import get_db
from app.core.security import get_current_user_token, check_permission
from app.models.user import User
from app.models.payment import Payment
from app.core.exceptions import (
    NotFoundError,
    AuthorizationError
)

router = APIRouter()


async def get_current_user(
    current_user_id: str = Depends(get_current_user_token),
    db: Session = Depends(get_db)
) -> User:
    """Get current authenticated user."""
    user = db.query(User).filter(User.id == current_user_id).first()
    if not user:
        raise NotFoundError("User not found")
    return user


@router.get("/")
async def get_payments(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get user payments.
    """
    # Build query based on user role
    if current_user.has_admin_access:
        # <PERSON><PERSON> can see all payments
        payments = db.query(Payment).all()
    else:
        # Users can only see their own payments
        payments = db.query(Payment).filter(
            Payment.user_id == current_user.id
        ).all()
    
    return [payment.to_dict() for payment in payments]


@router.get("/{payment_id}")
async def get_payment(
    payment_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get payment by ID.
    """
    payment = db.query(Payment).filter(Payment.id == payment_id).first()
    
    if not payment:
        raise NotFoundError("Payment not found")
    
    # Check permissions
    if (str(payment.user_id) != str(current_user.id) and 
        not current_user.has_admin_access):
        raise AuthorizationError()
    
    return payment.to_dict()


@router.post("/{payment_id}/webhook")
async def payment_webhook(
    payment_id: str,
    webhook_data: dict,
    db: Session = Depends(get_db)
):
    """
    Handle payment webhook from payment providers.
    """
    payment = db.query(Payment).filter(Payment.id == payment_id).first()
    
    if not payment:
        raise NotFoundError("Payment not found")
    
    # Update payment from webhook data
    payment.update_from_webhook(webhook_data)
    db.commit()
    
    return {"status": "success"}
