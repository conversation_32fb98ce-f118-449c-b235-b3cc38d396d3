"""
Authentication endpoints.
"""

from fastapi import API<PERSON>outer, Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>
from sqlalchemy.orm import Session
from datetime import timedelta

from app.core.database import get_db
from app.core.security import (
    verify_password,
    get_password_hash,
    create_access_token,
    create_refresh_token,
    verify_token,
    get_current_user_token
)
from app.core.config import settings
from app.models.user import User
from app.schemas.auth import (
    LoginResponse,
    RefreshTokenRequest,
    RefreshTokenResponse,
    LogoutResponse,
    ChangePasswordRequest,
    ChangePasswordResponse,
    ForgotPasswordRequest,
    ForgotPasswordResponse,
    ResetPasswordRequest,
    ResetPasswordResponse,
    VerifyEmailRequest,
    VerifyEmailResponse
)
from app.schemas.user import UserLogin, UserRegister, UserResponse
from app.core.exceptions import (
    InvalidCredentialsError,
    UserAlreadyExistsError,
    UserNotFoundError,
    AccountNotVerifiedError,
    AccountDisabledError
)

router = APIRouter()
security = HTTPBearer()


@router.post("/login", response_model=LoginResponse)
async def login(
    user_credentials: UserLogin,
    db: Session = Depends(get_db)
):
    """
    User login endpoint.
    """
    # Find user by email
    user = db.query(User).filter(User.email == user_credentials.email).first()
    
    if not user:
        raise InvalidCredentialsError()
    
    # Check if account is active
    if not user.is_active:
        raise AccountDisabledError()
    
    # Check if account is locked
    if user.is_locked:
        raise HTTPException(
            status_code=status.HTTP_423_LOCKED,
            detail="Account is temporarily locked due to too many failed login attempts"
        )
    
    # Verify password
    if not verify_password(user_credentials.password, user.hashed_password):
        user.increment_failed_login_attempts()
        db.commit()
        raise InvalidCredentialsError()
    
    # Check if email is verified (optional based on settings)
    # if not user.is_verified and settings.REQUIRE_EMAIL_VERIFICATION:
    #     raise AccountNotVerifiedError()
    
    # Reset failed login attempts on successful login
    user.reset_failed_login_attempts()
    user.update_last_login()
    db.commit()
    
    # Create tokens
    access_token = create_access_token(subject=str(user.id))
    refresh_token = create_refresh_token(subject=str(user.id))
    
    return LoginResponse(
        user=UserResponse.from_orm(user),
        tokens={
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        }
    )


@router.post("/register", response_model=UserResponse)
async def register(
    user_data: UserRegister,
    db: Session = Depends(get_db)
):
    """
    User registration endpoint.
    """
    # Check if user already exists
    existing_user = db.query(User).filter(User.email == user_data.email).first()
    if existing_user:
        raise UserAlreadyExistsError(user_data.email)
    
    # Create new user
    hashed_password = get_password_hash(user_data.password)
    
    new_user = User(
        email=user_data.email,
        full_name=user_data.full_name,
        phone=user_data.phone,
        hashed_password=hashed_password,
        role=user_data.role,
        is_active=True,
        is_verified=False  # Will be verified via email
    )
    
    # Generate email verification token
    from app.core.security import generate_verification_token
    new_user.email_verification_token = generate_verification_token()
    
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    
    # TODO: Send verification email
    # await send_verification_email(new_user.email, new_user.email_verification_token)
    
    return UserResponse.from_orm(new_user)


@router.post("/refresh", response_model=RefreshTokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """
    Refresh access token using refresh token.
    """
    user_id = verify_token(refresh_data.refresh_token, "refresh")
    
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    # Verify user still exists and is active
    user = db.query(User).filter(User.id == user_id).first()
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    
    # Create new access token
    access_token = create_access_token(subject=user_id)
    
    return RefreshTokenResponse(
        access_token=access_token,
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


@router.post("/logout", response_model=LogoutResponse)
async def logout():
    """
    User logout endpoint.
    Note: In a stateless JWT implementation, logout is handled client-side
    by removing the tokens. For enhanced security, you could implement
    a token blacklist using Redis.
    """
    # TODO: Add token to blacklist in Redis
    return LogoutResponse()


@router.post("/change-password", response_model=ChangePasswordResponse)
async def change_password(
    password_data: ChangePasswordRequest,
    current_user_id: str = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """
    Change user password.
    """
    user = db.query(User).filter(User.id == current_user_id).first()
    if not user:
        raise UserNotFoundError(current_user_id)
    
    # Verify current password
    if not verify_password(password_data.current_password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    # Update password
    from datetime import datetime
    user.hashed_password = get_password_hash(password_data.new_password)
    user.password_changed_at = datetime.utcnow()
    
    db.commit()
    
    return ChangePasswordResponse()


@router.post("/forgot-password", response_model=ForgotPasswordResponse)
async def forgot_password(
    request_data: ForgotPasswordRequest,
    db: Session = Depends(get_db)
):
    """
    Request password reset.
    """
    user = db.query(User).filter(User.email == request_data.email).first()
    
    # Always return success to prevent email enumeration
    if user and user.is_active:
        from app.core.security import generate_password_reset_token
        from datetime import datetime, timedelta
        
        # Generate reset token
        reset_token = generate_password_reset_token(user.email)
        user.password_reset_token = reset_token
        user.password_reset_expires = datetime.utcnow() + timedelta(hours=24)
        
        db.commit()
        
        # TODO: Send password reset email
        # await send_password_reset_email(user.email, reset_token)
    
    return ForgotPasswordResponse()


@router.post("/reset-password", response_model=ResetPasswordResponse)
async def reset_password(
    reset_data: ResetPasswordRequest,
    db: Session = Depends(get_db)
):
    """
    Reset password using reset token.
    """
    from app.core.security import verify_password_reset_token
    
    # Verify reset token
    email = verify_password_reset_token(reset_data.token)
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )
    
    # Find user and verify token matches
    user = db.query(User).filter(
        User.email == email,
        User.password_reset_token == reset_data.token
    ).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid reset token"
        )
    
    # Check if token is expired
    if user.password_reset_expires and user.password_reset_expires < datetime.utcnow():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Reset token has expired"
        )
    
    # Update password
    user.hashed_password = get_password_hash(reset_data.new_password)
    user.password_reset_token = None
    user.password_reset_expires = None
    user.password_changed_at = datetime.utcnow()
    
    # Reset failed login attempts
    user.reset_failed_login_attempts()
    
    db.commit()
    
    return ResetPasswordResponse()


@router.post("/verify-email", response_model=VerifyEmailResponse)
async def verify_email(
    verification_data: VerifyEmailRequest,
    db: Session = Depends(get_db)
):
    """
    Verify user email address.
    """
    user = db.query(User).filter(
        User.email_verification_token == verification_data.token
    ).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid verification token"
        )
    
    # Mark email as verified
    user.is_verified = True
    user.email_verified_at = datetime.utcnow()
    user.email_verification_token = None
    
    db.commit()
    
    return VerifyEmailResponse(
        user=UserResponse.from_orm(user)
    )


@router.get("/me", response_model=UserResponse)
async def get_current_user(
    current_user_id: str = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """
    Get current authenticated user.
    """
    user = db.query(User).filter(User.id == current_user_id).first()
    if not user:
        raise UserNotFoundError(current_user_id)
    
    return UserResponse.from_orm(user)
