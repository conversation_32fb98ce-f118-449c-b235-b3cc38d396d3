"""
User management endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional

from app.core.database import get_db
from app.core.security import get_current_user_token, check_permission
from app.models.user import User, UserRole
from app.schemas.user import (
    UserResponse,
    UserUpdate,
    UserList,
    UserStats,
    UserProfile
)
from app.core.exceptions import (
    UserNotFoundError,
    AuthorizationError
)

router = APIRouter()


async def get_current_user(
    current_user_id: str = Depends(get_current_user_token),
    db: Session = Depends(get_db)
) -> User:
    """Get current authenticated user."""
    user = db.query(User).filter(User.id == current_user_id).first()
    if not user:
        raise UserNotFoundError(current_user_id)
    return user


@router.get("/", response_model=UserList)
async def get_users(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    role: Optional[UserRole] = None,
    is_active: Optional[bool] = None,
    search: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get list of users (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "manage_users"):
        raise AuthorizationError()
    
    # Build query
    query = db.query(User).filter(User.deleted_at.is_(None))
    
    # Apply filters
    if role:
        query = query.filter(User.role == role)
    
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            (User.full_name.ilike(search_term)) |
            (User.email.ilike(search_term)) |
            (User.phone.ilike(search_term))
        )
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    offset = (page - 1) * per_page
    users = query.offset(offset).limit(per_page).all()
    
    # Calculate pages
    pages = (total + per_page - 1) // per_page
    
    return UserList(
        users=[UserResponse.from_orm(user) for user in users],
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.get("/me", response_model=UserProfile)
async def get_my_profile(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user's profile.
    """
    return UserProfile.from_orm(current_user)


@router.put("/me", response_model=UserResponse)
async def update_my_profile(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update current user's profile.
    """
    # Update user fields
    update_data = user_update.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        if hasattr(current_user, field):
            setattr(current_user, field, value)
    
    db.commit()
    db.refresh(current_user)
    
    return UserResponse.from_orm(current_user)


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get user by ID.
    """
    # Users can view their own profile, admins can view any profile
    if str(current_user.id) != user_id and not check_permission(current_user.role, "manage_users"):
        raise AuthorizationError()
    
    user = db.query(User).filter(
        User.id == user_id,
        User.deleted_at.is_(None)
    ).first()
    
    if not user:
        raise UserNotFoundError(user_id)
    
    return UserResponse.from_orm(user)


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: str,
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update user by ID (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "manage_users"):
        raise AuthorizationError()
    
    user = db.query(User).filter(
        User.id == user_id,
        User.deleted_at.is_(None)
    ).first()
    
    if not user:
        raise UserNotFoundError(user_id)
    
    # Update user fields
    update_data = user_update.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        if hasattr(user, field):
            setattr(user, field, value)
    
    db.commit()
    db.refresh(user)
    
    return UserResponse.from_orm(user)


@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Soft delete user by ID (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "manage_users"):
        raise AuthorizationError()
    
    # Prevent self-deletion
    if str(current_user.id) == user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )
    
    user = db.query(User).filter(
        User.id == user_id,
        User.deleted_at.is_(None)
    ).first()
    
    if not user:
        raise UserNotFoundError(user_id)
    
    # Soft delete
    user.soft_delete()
    db.commit()
    
    return {"message": "User deleted successfully"}


@router.post("/{user_id}/activate")
async def activate_user(
    user_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Activate user account (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "manage_users"):
        raise AuthorizationError()
    
    user = db.query(User).filter(User.id == user_id).first()
    
    if not user:
        raise UserNotFoundError(user_id)
    
    user.is_active = True
    user.restore()  # Remove soft delete if applicable
    db.commit()
    
    return {"message": "User activated successfully"}


@router.post("/{user_id}/deactivate")
async def deactivate_user(
    user_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Deactivate user account (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "manage_users"):
        raise AuthorizationError()
    
    # Prevent self-deactivation
    if str(current_user.id) == user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot deactivate your own account"
        )
    
    user = db.query(User).filter(User.id == user_id).first()
    
    if not user:
        raise UserNotFoundError(user_id)
    
    user.is_active = False
    db.commit()
    
    return {"message": "User deactivated successfully"}


@router.get("/stats/overview", response_model=UserStats)
async def get_user_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get user statistics (admin only).
    """
    # Check permissions
    if not check_permission(current_user.role, "view_reports"):
        raise AuthorizationError()
    
    # Get user counts
    total_users = db.query(User).filter(User.deleted_at.is_(None)).count()
    active_users = db.query(User).filter(
        User.deleted_at.is_(None),
        User.is_active == True
    ).count()
    verified_users = db.query(User).filter(
        User.deleted_at.is_(None),
        User.is_verified == True
    ).count()
    
    # Get counts by role
    clients = db.query(User).filter(
        User.deleted_at.is_(None),
        User.role == UserRole.CLIENT
    ).count()
    professionals = db.query(User).filter(
        User.deleted_at.is_(None),
        User.role == UserRole.PROFESSIONAL
    ).count()
    admins = db.query(User).filter(
        User.deleted_at.is_(None),
        User.role.in_([UserRole.ADMIN, UserRole.OWNER])
    ).count()
    
    # Get new users this month
    from datetime import datetime, timedelta
    start_of_month = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    new_users_this_month = db.query(User).filter(
        User.deleted_at.is_(None),
        User.created_at >= start_of_month
    ).count()
    
    return UserStats(
        total_users=total_users,
        active_users=active_users,
        verified_users=verified_users,
        clients=clients,
        professionals=professionals,
        admins=admins,
        new_users_this_month=new_users_this_month
    )
