"""
Appointment management endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from typing import List, Optional
from datetime import datetime, date, timedelta

from app.core.database import get_db
from app.core.security import get_current_user_token, check_permission
from app.models.user import User
from app.models.appointment import Appointment, AppointmentStatus
from app.models.service import Service
from app.models.professional import Professional
from app.schemas.appointment import (
    AppointmentCreate,
    AppointmentUpdate,
    AppointmentResponse,
    AppointmentWithDetails,
    AppointmentList,
    AppointmentReschedule,
    AppointmentCancel,
    AppointmentConfirm,
    AppointmentComplete,
    AppointmentRating,
    AppointmentAvailability,
    AppointmentStats
)
from app.core.exceptions import (
    NotFoundError,
    AuthorizationError,
    ValidationError,
    BusinessRuleError,
    AppointmentNotAvailableError,
    AppointmentTooEarlyError
)

router = APIRouter()


async def get_current_user(
    current_user_id: str = Depends(get_current_user_token),
    db: Session = Depends(get_db)
) -> User:
    """Get current authenticated user."""
    user = db.query(User).filter(User.id == current_user_id).first()
    if not user:
        raise NotFoundError("User not found")
    return user


def validate_appointment_time(
    appointment_date: datetime,
    service: Service,
    professional: Professional
) -> None:
    """Validate appointment timing rules."""
    now = datetime.utcnow()
    
    # Check minimum advance booking time
    min_advance = timedelta(hours=service.min_advance_hours)
    if appointment_date < now + min_advance:
        raise AppointmentTooEarlyError(service.min_advance_hours)
    
    # Check maximum advance booking time
    max_advance = timedelta(days=service.advance_booking_days)
    if appointment_date > now + max_advance:
        raise ValidationError(f"Cannot book more than {service.advance_booking_days} days in advance")
    
    # Check if professional is working at that time
    day_of_week = appointment_date.weekday()
    appointment_time = appointment_date.time()
    
    if not professional.is_working_at_time(day_of_week, appointment_time):
        raise ValidationError("Professional is not working at the requested time")


def check_appointment_availability(
    professional_id: str,
    appointment_date: datetime,
    duration_minutes: int,
    exclude_appointment_id: Optional[str] = None,
    db: Session = None
) -> bool:
    """Check if appointment slot is available."""
    end_time = appointment_date + timedelta(minutes=duration_minutes)
    
    # Check for conflicting appointments
    query = db.query(Appointment).filter(
        Appointment.professional_id == professional_id,
        Appointment.status.in_([
            AppointmentStatus.SCHEDULED,
            AppointmentStatus.CONFIRMED,
            AppointmentStatus.IN_PROGRESS
        ]),
        or_(
            and_(
                Appointment.appointment_date <= appointment_date,
                Appointment.appointment_date + timedelta(minutes=Appointment.duration_minutes) > appointment_date
            ),
            and_(
                Appointment.appointment_date < end_time,
                Appointment.appointment_date + timedelta(minutes=Appointment.duration_minutes) >= end_time
            ),
            and_(
                Appointment.appointment_date >= appointment_date,
                Appointment.appointment_date < end_time
            )
        )
    )
    
    if exclude_appointment_id:
        query = query.filter(Appointment.id != exclude_appointment_id)
    
    conflicting_appointments = query.count()
    return conflicting_appointments == 0


@router.get("/", response_model=AppointmentList)
async def get_appointments(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    status: Optional[AppointmentStatus] = None,
    professional_id: Optional[str] = None,
    service_id: Optional[str] = None,
    date_from: Optional[datetime] = None,
    date_to: Optional[datetime] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get appointments with filtering and pagination.
    """
    # Build base query
    query = db.query(Appointment)
    
    # Apply role-based filtering
    if current_user.is_client:
        # Clients can only see their own appointments
        query = query.filter(Appointment.client_id == current_user.id)
    elif current_user.is_professional:
        # Professionals can see their assigned appointments
        professional = db.query(Professional).filter(
            Professional.user_id == current_user.id
        ).first()
        if professional:
            query = query.filter(Appointment.professional_id == professional.id)
        else:
            # User is marked as professional but has no professional profile
            query = query.filter(Appointment.client_id == current_user.id)
    elif not current_user.has_admin_access:
        # Non-admin users can only see their own appointments
        query = query.filter(Appointment.client_id == current_user.id)
    
    # Apply filters
    if status:
        query = query.filter(Appointment.status == status)
    
    if professional_id:
        query = query.filter(Appointment.professional_id == professional_id)
    
    if service_id:
        query = query.filter(Appointment.service_id == service_id)
    
    if date_from:
        query = query.filter(Appointment.appointment_date >= date_from)
    
    if date_to:
        query = query.filter(Appointment.appointment_date <= date_to)
    
    # Order by appointment date
    query = query.order_by(desc(Appointment.appointment_date))
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    offset = (page - 1) * per_page
    appointments = query.offset(offset).limit(per_page).all()
    
    # Calculate pages
    pages = (total + per_page - 1) // per_page
    
    return AppointmentList(
        appointments=[AppointmentResponse.from_orm(apt) for apt in appointments],
        total=total,
        page=page,
        per_page=per_page,
        pages=pages
    )


@router.get("/{appointment_id}", response_model=AppointmentWithDetails)
async def get_appointment(
    appointment_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get appointment by ID with details.
    """
    appointment = db.query(Appointment).filter(
        Appointment.id == appointment_id
    ).first()
    
    if not appointment:
        raise NotFoundError("Appointment not found")
    
    # Check permissions
    can_view = False
    if current_user.has_admin_access:
        can_view = True
    elif str(appointment.client_id) == str(current_user.id):
        can_view = True
    elif current_user.is_professional:
        professional = db.query(Professional).filter(
            Professional.user_id == current_user.id
        ).first()
        if professional and str(appointment.professional_id) == str(professional.id):
            can_view = True
    
    if not can_view:
        raise AuthorizationError()
    
    # Load related data
    appointment_dict = AppointmentResponse.from_orm(appointment).dict()
    
    # Add related details
    appointment_dict['client'] = appointment.client
    appointment_dict['service'] = appointment.service
    appointment_dict['professional'] = {
        'id': str(appointment.professional.id),
        'full_name': appointment.professional.full_name,
        'specialties': appointment.professional.specialties,
        'avatar_url': appointment.professional.avatar_url
    }
    
    return AppointmentWithDetails(**appointment_dict)


@router.post("/", response_model=AppointmentResponse)
async def create_appointment(
    appointment_data: AppointmentCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create new appointment.
    """
    # Get service and professional
    service = db.query(Service).filter(
        Service.id == appointment_data.service_id,
        Service.is_active == True
    ).first()
    
    if not service:
        raise NotFoundError("Service not found or inactive")
    
    professional = db.query(Professional).filter(
        Professional.id == appointment_data.professional_id,
        Professional.is_available == True
    ).first()
    
    if not professional:
        raise NotFoundError("Professional not found or unavailable")
    
    # Validate appointment time
    validate_appointment_time(
        appointment_data.appointment_date,
        service,
        professional
    )
    
    # Check availability
    if not check_appointment_availability(
        appointment_data.professional_id,
        appointment_data.appointment_date,
        service.duration_minutes,
        db=db
    ):
        raise AppointmentNotAvailableError(
            appointment_data.appointment_date.strftime("%d/%m/%Y %H:%M")
        )
    
    # Create appointment
    appointment = Appointment(
        client_id=current_user.id,
        professional_id=appointment_data.professional_id,
        service_id=appointment_data.service_id,
        appointment_date=appointment_data.appointment_date,
        duration_minutes=service.duration_minutes,
        service_price=service.current_price,
        total_price=service.current_price,
        client_notes=appointment_data.client_notes
    )
    
    appointment.calculate_total_price()
    
    db.add(appointment)
    db.commit()
    db.refresh(appointment)
    
    # Update service booking count
    service.increment_booking_count()
    db.commit()
    
    # TODO: Send confirmation notification
    
    return AppointmentResponse.from_orm(appointment)


@router.put("/{appointment_id}", response_model=AppointmentResponse)
async def update_appointment(
    appointment_id: str,
    appointment_update: AppointmentUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update appointment.
    """
    appointment = db.query(Appointment).filter(
        Appointment.id == appointment_id
    ).first()
    
    if not appointment:
        raise NotFoundError("Appointment not found")
    
    # Check permissions
    can_edit = False
    if current_user.has_admin_access:
        can_edit = True
    elif str(appointment.client_id) == str(current_user.id):
        # Clients can edit their own appointments if not confirmed
        can_edit = not appointment.is_confirmed
    elif current_user.is_professional:
        professional = db.query(Professional).filter(
            Professional.user_id == current_user.id
        ).first()
        if professional and str(appointment.professional_id) == str(professional.id):
            can_edit = True
    
    if not can_edit:
        raise AuthorizationError()
    
    # Update appointment fields
    update_data = appointment_update.dict(exclude_unset=True)
    
    # If rescheduling, validate new time
    if 'appointment_date' in update_data:
        new_date = update_data['appointment_date']
        
        # Validate new appointment time
        validate_appointment_time(new_date, appointment.service, appointment.professional)
        
        # Check availability for new time
        if not check_appointment_availability(
            appointment.professional_id,
            new_date,
            appointment.duration_minutes,
            exclude_appointment_id=appointment_id,
            db=db
        ):
            raise AppointmentNotAvailableError(new_date.strftime("%d/%m/%Y %H:%M"))
    
    for field, value in update_data.items():
        if hasattr(appointment, field):
            setattr(appointment, field, value)
    
    db.commit()
    db.refresh(appointment)
    
    return AppointmentResponse.from_orm(appointment)


@router.post("/{appointment_id}/confirm", response_model=AppointmentResponse)
async def confirm_appointment(
    appointment_id: str,
    confirm_data: AppointmentConfirm,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Confirm appointment.
    """
    appointment = db.query(Appointment).filter(
        Appointment.id == appointment_id
    ).first()
    
    if not appointment:
        raise NotFoundError("Appointment not found")
    
    # Check permissions (professional or admin can confirm)
    can_confirm = False
    if current_user.has_admin_access:
        can_confirm = True
    elif current_user.is_professional:
        professional = db.query(Professional).filter(
            Professional.user_id == current_user.id
        ).first()
        if professional and str(appointment.professional_id) == str(professional.id):
            can_confirm = True
    
    if not can_confirm:
        raise AuthorizationError()
    
    if appointment.status != AppointmentStatus.SCHEDULED:
        raise ValidationError("Only scheduled appointments can be confirmed")
    
    appointment.confirm(confirm_data.confirmation_method)
    db.commit()
    
    # TODO: Send confirmation notification to client
    
    return AppointmentResponse.from_orm(appointment)


@router.post("/{appointment_id}/cancel", response_model=AppointmentResponse)
async def cancel_appointment(
    appointment_id: str,
    cancel_data: AppointmentCancel,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Cancel appointment.
    """
    appointment = db.query(Appointment).filter(
        Appointment.id == appointment_id
    ).first()
    
    if not appointment:
        raise NotFoundError("Appointment not found")
    
    # Check permissions
    can_cancel = False
    cancelled_by = "system"
    
    if current_user.has_admin_access:
        can_cancel = True
        cancelled_by = "admin"
    elif str(appointment.client_id) == str(current_user.id):
        can_cancel = appointment.can_be_cancelled
        cancelled_by = "client"
    elif current_user.is_professional:
        professional = db.query(Professional).filter(
            Professional.user_id == current_user.id
        ).first()
        if professional and str(appointment.professional_id) == str(professional.id):
            can_cancel = True
            cancelled_by = "professional"
    
    if not can_cancel:
        if not appointment.can_be_cancelled:
            raise BusinessRuleError("Appointment cannot be cancelled at this time")
        else:
            raise AuthorizationError()
    
    # Calculate cancellation fee if applicable
    cancellation_fee = appointment.service.get_cancellation_fee(appointment.total_price)
    
    appointment.cancel(cancelled_by, cancel_data.reason, cancellation_fee)
    db.commit()
    
    # TODO: Send cancellation notification
    
    return AppointmentResponse.from_orm(appointment)


@router.post("/{appointment_id}/complete", response_model=AppointmentResponse)
async def complete_appointment(
    appointment_id: str,
    complete_data: AppointmentComplete,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Mark appointment as completed.
    """
    appointment = db.query(Appointment).filter(
        Appointment.id == appointment_id
    ).first()
    
    if not appointment:
        raise NotFoundError("Appointment not found")
    
    # Check permissions (professional or admin can complete)
    can_complete = False
    if current_user.has_admin_access:
        can_complete = True
    elif current_user.is_professional:
        professional = db.query(Professional).filter(
            Professional.user_id == current_user.id
        ).first()
        if professional and str(appointment.professional_id) == str(professional.id):
            can_complete = True
    
    if not can_complete:
        raise AuthorizationError()
    
    if appointment.status not in [AppointmentStatus.CONFIRMED, AppointmentStatus.IN_PROGRESS]:
        raise ValidationError("Only confirmed or in-progress appointments can be completed")
    
    appointment.complete(complete_data.actual_duration_minutes)
    
    if complete_data.professional_notes:
        appointment.professional_notes = complete_data.professional_notes
    
    db.commit()
    
    # Update professional stats
    appointment.professional.increment_appointment_count("completed")
    db.commit()
    
    return AppointmentResponse.from_orm(appointment)


@router.post("/{appointment_id}/rate", response_model=AppointmentResponse)
async def rate_appointment(
    appointment_id: str,
    rating_data: AppointmentRating,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Rate completed appointment.
    """
    appointment = db.query(Appointment).filter(
        Appointment.id == appointment_id
    ).first()
    
    if not appointment:
        raise NotFoundError("Appointment not found")
    
    # Only client can rate their appointment
    if str(appointment.client_id) != str(current_user.id):
        raise AuthorizationError()
    
    if appointment.status != AppointmentStatus.COMPLETED:
        raise ValidationError("Only completed appointments can be rated")
    
    if appointment.client_rating is not None:
        raise ValidationError("Appointment has already been rated")
    
    appointment.add_rating(rating_data.rating, rating_data.feedback)
    db.commit()
    
    # Update service and professional ratings
    appointment.service.update_rating(rating_data.rating)
    appointment.professional.update_rating(rating_data.rating)
    db.commit()
    
    return AppointmentResponse.from_orm(appointment)
