"""
User model for authentication and user management.
"""

from sqlalchemy import Column, Inte<PERSON>, String, Boolean, DateTime, Text, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum

from app.core.database import Base


class UserRole(str, enum.Enum):
    """User roles enum."""
    CLIENT = "client"
    PROFESSIONAL = "professional"
    ADMIN = "admin"
    OWNER = "owner"


class User(Base):
    """User model."""
    
    __tablename__ = "users"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Basic information
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(255), nullable=False)
    phone = Column(String(20), nullable=True)
    
    # Authentication
    hashed_password = Column(String(255), nullable=False)
    role = Column(Enum(UserRole), default=UserRole.CLIENT, nullable=False)
    
    # Account status
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    
    # Profile information
    avatar_url = Column(String(500), nullable=True)
    bio = Column(Text, nullable=True)
    date_of_birth = Column(DateTime, nullable=True)
    
    # Address information
    address = Column(Text, nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(50), nullable=True)
    zip_code = Column(String(20), nullable=True)
    country = Column(String(50), default="Brasil", nullable=True)
    
    # Preferences
    preferred_language = Column(String(10), default="pt-BR", nullable=True)
    timezone = Column(String(50), default="America/Sao_Paulo", nullable=True)
    
    # Notification preferences
    email_notifications = Column(Boolean, default=True, nullable=False)
    sms_notifications = Column(Boolean, default=True, nullable=False)
    whatsapp_notifications = Column(Boolean, default=True, nullable=False)
    
    # Security
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime, nullable=True)
    last_login = Column(DateTime, nullable=True)
    password_changed_at = Column(DateTime, default=func.now(), nullable=False)
    
    # Email verification
    email_verification_token = Column(String(255), nullable=True)
    email_verified_at = Column(DateTime, nullable=True)
    
    # Password reset
    password_reset_token = Column(String(255), nullable=True)
    password_reset_expires = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime, nullable=True)  # Soft delete
    
    # Relationships
    appointments_as_client = relationship(
        "Appointment",
        foreign_keys="Appointment.client_id",
        back_populates="client",
        cascade="all, delete-orphan"
    )
    
    professional_profile = relationship(
        "Professional",
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan"
    )
    
    payments = relationship(
        "Payment",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    notifications = relationship(
        "Notification",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self):
        return f"<User(id={self.id}, email={self.email}, role={self.role})>"
    
    @property
    def is_client(self) -> bool:
        """Check if user is a client."""
        return self.role == UserRole.CLIENT
    
    @property
    def is_professional(self) -> bool:
        """Check if user is a professional."""
        return self.role == UserRole.PROFESSIONAL
    
    @property
    def is_admin(self) -> bool:
        """Check if user is an admin."""
        return self.role == UserRole.ADMIN
    
    @property
    def is_owner(self) -> bool:
        """Check if user is an owner."""
        return self.role == UserRole.OWNER
    
    @property
    def has_admin_access(self) -> bool:
        """Check if user has admin access."""
        return self.role in [UserRole.ADMIN, UserRole.OWNER]
    
    @property
    def is_locked(self) -> bool:
        """Check if account is locked."""
        if not self.locked_until:
            return False
        from datetime import datetime
        return datetime.utcnow() < self.locked_until
    
    @property
    def full_address(self) -> str:
        """Get formatted full address."""
        parts = []
        if self.address:
            parts.append(self.address)
        if self.city:
            parts.append(self.city)
        if self.state:
            parts.append(self.state)
        if self.zip_code:
            parts.append(self.zip_code)
        if self.country:
            parts.append(self.country)
        return ", ".join(parts)
    
    def can_receive_notifications(self, notification_type: str) -> bool:
        """Check if user can receive specific type of notifications."""
        if notification_type == "email":
            return self.email_notifications and self.is_verified
        elif notification_type == "sms":
            return self.sms_notifications and self.phone
        elif notification_type == "whatsapp":
            return self.whatsapp_notifications and self.phone
        return False
    
    def reset_failed_login_attempts(self):
        """Reset failed login attempts."""
        self.failed_login_attempts = 0
        self.locked_until = None
    
    def increment_failed_login_attempts(self):
        """Increment failed login attempts and lock if necessary."""
        from datetime import datetime, timedelta
        from app.core.config import settings
        
        self.failed_login_attempts += 1
        
        if self.failed_login_attempts >= settings.MAX_LOGIN_ATTEMPTS:
            self.locked_until = datetime.utcnow() + timedelta(
                minutes=settings.LOCKOUT_DURATION_MINUTES
            )
    
    def update_last_login(self):
        """Update last login timestamp."""
        from datetime import datetime
        self.last_login = datetime.utcnow()
    
    def soft_delete(self):
        """Soft delete user."""
        from datetime import datetime
        self.deleted_at = datetime.utcnow()
        self.is_active = False
    
    def restore(self):
        """Restore soft deleted user."""
        self.deleted_at = None
        self.is_active = True
