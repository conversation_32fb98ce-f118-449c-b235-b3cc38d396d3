"""
Service model for salon services.
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Numeric, JSON
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from decimal import Decimal
from typing import List, Dict, Any

from app.core.database import Base


class Service(Base):
    """Service model."""
    
    __tablename__ = "services"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Basic information
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=False, index=True)
    
    # Pricing
    price = Column(Numeric(10, 2), nullable=False)
    promotional_price = Column(Numeric(10, 2), nullable=True)
    
    # Duration and scheduling
    duration_minutes = Column(Integer, nullable=False)
    buffer_time_minutes = Column(Integer, default=15, nullable=False)  # Time between appointments
    
    # Availability
    is_active = Column(Boolean, default=True, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False)
    requires_consultation = Column(Boolean, default=False, nullable=False)
    
    # Service details
    image_url = Column(String(500), nullable=True)
    gallery_urls = Column(ARRAY(String), nullable=True)  # Multiple images
    
    # Requirements and restrictions
    min_age = Column(Integer, nullable=True)
    max_age = Column(Integer, nullable=True)
    gender_restriction = Column(String(20), nullable=True)  # 'male', 'female', 'any'
    
    # Preparation instructions
    preparation_instructions = Column(Text, nullable=True)
    aftercare_instructions = Column(Text, nullable=True)
    
    # Professional requirements
    required_skills = Column(ARRAY(String), nullable=True)
    difficulty_level = Column(Integer, default=1, nullable=False)  # 1-5 scale
    
    # Booking settings
    advance_booking_days = Column(Integer, default=30, nullable=False)
    min_advance_hours = Column(Integer, default=2, nullable=False)
    max_daily_bookings = Column(Integer, nullable=True)
    
    # Cancellation policy
    cancellation_hours = Column(Integer, default=24, nullable=False)
    cancellation_fee_percentage = Column(Numeric(5, 2), default=0, nullable=False)
    
    # SEO and marketing
    slug = Column(String(255), unique=True, nullable=True, index=True)
    meta_title = Column(String(255), nullable=True)
    meta_description = Column(Text, nullable=True)
    tags = Column(ARRAY(String), nullable=True)
    
    # Analytics
    booking_count = Column(Integer, default=0, nullable=False)
    rating_average = Column(Numeric(3, 2), nullable=True)
    rating_count = Column(Integer, default=0, nullable=False)
    
    # Additional data
    extra_data = Column(JSON, nullable=True)  # For flexible additional information
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime, nullable=True)  # Soft delete
    
    # Relationships
    appointments = relationship(
        "Appointment",
        back_populates="service",
        cascade="all, delete-orphan"
    )
    
    professional_services = relationship(
        "ProfessionalService",
        back_populates="service",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self):
        return f"<Service(id={self.id}, name={self.name}, category={self.category})>"
    
    @property
    def current_price(self) -> Decimal:
        """Get current price (promotional if available)."""
        if self.promotional_price and self.promotional_price > 0:
            return self.promotional_price
        return self.price
    
    @property
    def has_promotion(self) -> bool:
        """Check if service has promotional price."""
        return bool(self.promotional_price and self.promotional_price < self.price)
    
    @property
    def discount_percentage(self) -> float:
        """Calculate discount percentage."""
        if not self.has_promotion:
            return 0.0
        return float((self.price - self.promotional_price) / self.price * 100)
    
    @property
    def total_duration_minutes(self) -> int:
        """Get total duration including buffer time."""
        return self.duration_minutes + self.buffer_time_minutes
    
    @property
    def formatted_duration(self) -> str:
        """Get formatted duration string."""
        hours = self.duration_minutes // 60
        minutes = self.duration_minutes % 60
        
        if hours > 0 and minutes > 0:
            return f"{hours}h {minutes}min"
        elif hours > 0:
            return f"{hours}h"
        else:
            return f"{minutes}min"
    
    @property
    def is_available_for_booking(self) -> bool:
        """Check if service is available for booking."""
        return self.is_active and not self.deleted_at
    
    @property
    def average_rating_stars(self) -> int:
        """Get average rating as stars (1-5)."""
        if not self.rating_average:
            return 0
        return round(float(self.rating_average))
    
    def can_be_booked_by_age(self, age: int) -> bool:
        """Check if service can be booked by person of given age."""
        if self.min_age and age < self.min_age:
            return False
        if self.max_age and age > self.max_age:
            return False
        return True
    
    def can_be_booked_by_gender(self, gender: str) -> bool:
        """Check if service can be booked by person of given gender."""
        if not self.gender_restriction or self.gender_restriction == "any":
            return True
        return gender.lower() == self.gender_restriction.lower()
    
    def increment_booking_count(self):
        """Increment booking count."""
        self.booking_count += 1
    
    def update_rating(self, new_rating: float):
        """Update service rating."""
        if self.rating_count == 0:
            self.rating_average = Decimal(str(new_rating))
            self.rating_count = 1
        else:
            current_total = float(self.rating_average) * self.rating_count
            new_total = current_total + new_rating
            self.rating_count += 1
            self.rating_average = Decimal(str(new_total / self.rating_count))
    
    def get_cancellation_fee(self, appointment_price: Decimal) -> Decimal:
        """Calculate cancellation fee for given appointment price."""
        if self.cancellation_fee_percentage == 0:
            return Decimal('0.00')
        return appointment_price * (self.cancellation_fee_percentage / 100)
    
    def soft_delete(self):
        """Soft delete service."""
        from datetime import datetime
        self.deleted_at = datetime.utcnow()
        self.is_active = False
    
    def restore(self):
        """Restore soft deleted service."""
        self.deleted_at = None
        self.is_active = True
    
    def generate_slug(self):
        """Generate URL slug from name."""
        import re
        slug = self.name.lower()
        slug = re.sub(r'[^\w\s-]', '', slug)
        slug = re.sub(r'[-\s]+', '-', slug)
        self.slug = slug.strip('-')
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert service to dictionary."""
        return {
            'id': str(self.id),
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'price': float(self.price),
            'promotional_price': float(self.promotional_price) if self.promotional_price else None,
            'current_price': float(self.current_price),
            'duration_minutes': self.duration_minutes,
            'formatted_duration': self.formatted_duration,
            'is_active': self.is_active,
            'is_featured': self.is_featured,
            'image_url': self.image_url,
            'rating_average': float(self.rating_average) if self.rating_average else None,
            'rating_count': self.rating_count,
            'has_promotion': self.has_promotion,
            'discount_percentage': self.discount_percentage
        }
