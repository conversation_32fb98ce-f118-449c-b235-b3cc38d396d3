"""
Professional Schedule model for managing availability and time blocks.
"""

from sqlalchemy import <PERSON>um<PERSON>, Inte<PERSON>, String, Boolean, DateTime, Time, Date, Text, ForeignKey, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum
from typing import Dict, Any, List
from datetime import datetime, date, time, timedelta

from app.core.database import Base


class ScheduleType(str, enum.Enum):
    """Schedule type enum."""
    AVAILABLE = "available"
    BREAK = "break"
    BLOCKED = "blocked"
    VACATION = "vacation"


class ProfessionalSchedule(Base):
    """Professional Schedule model."""
    
    __tablename__ = "professional_schedules"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign key
    professional_id = Column(UUID(as_uuid=True), ForeignKey("professionals.id"), nullable=False, index=True)
    
    # Schedule details
    schedule_date = Column(Date, nullable=False, index=True)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    schedule_type = Column(Enum(ScheduleType), default=ScheduleType.AVAILABLE, nullable=False)
    
    # Recurrence (for repeating schedules)
    is_recurring = Column(Boolean, default=False, nullable=False)
    recurrence_pattern = Column(String(50), nullable=True)  # daily, weekly, monthly
    recurrence_end_date = Column(Date, nullable=True)
    
    # Additional details
    title = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    
    # Override settings
    is_override = Column(Boolean, default=False, nullable=False)  # Override default working hours
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    professional = relationship("Professional", back_populates="schedules")
    
    def __repr__(self):
        return f"<ProfessionalSchedule(id={self.id}, date={self.schedule_date}, type={self.schedule_type})>"
    
    @property
    def duration_minutes(self) -> int:
        """Calculate duration in minutes."""
        start_datetime = datetime.combine(date.today(), self.start_time)
        end_datetime = datetime.combine(date.today(), self.end_time)
        
        # Handle overnight schedules
        if end_datetime < start_datetime:
            end_datetime = datetime.combine(date.today() + timedelta(days=1), self.end_time)
        
        duration = end_datetime - start_datetime
        return int(duration.total_seconds() / 60)
    
    @property
    def is_available_slot(self) -> bool:
        """Check if this is an available time slot."""
        return self.schedule_type == ScheduleType.AVAILABLE
    
    @property
    def is_blocked_slot(self) -> bool:
        """Check if this is a blocked time slot."""
        return self.schedule_type in [ScheduleType.BLOCKED, ScheduleType.BREAK, ScheduleType.VACATION]
    
    @property
    def is_past(self) -> bool:
        """Check if schedule is in the past."""
        schedule_datetime = datetime.combine(self.schedule_date, self.end_time)
        return schedule_datetime < datetime.now()
    
    @property
    def is_today(self) -> bool:
        """Check if schedule is today."""
        return self.schedule_date == date.today()
    
    @property
    def is_future(self) -> bool:
        """Check if schedule is in the future."""
        return self.schedule_date > date.today()
    
    def overlaps_with(self, other_start: time, other_end: time) -> bool:
        """Check if this schedule overlaps with given time range."""
        return not (self.end_time <= other_start or self.start_time >= other_end)
    
    def contains_time(self, check_time: time) -> bool:
        """Check if given time is within this schedule."""
        return self.start_time <= check_time <= self.end_time
    
    def can_fit_appointment(self, duration_minutes: int, appointment_time: time) -> bool:
        """Check if an appointment can fit in this schedule slot."""
        if not self.is_available_slot:
            return False
        
        # Calculate appointment end time
        appointment_start = datetime.combine(date.today(), appointment_time)
        appointment_end = appointment_start + timedelta(minutes=duration_minutes)
        appointment_end_time = appointment_end.time()
        
        # Check if appointment fits within the available slot
        return (
            self.start_time <= appointment_time and
            appointment_end_time <= self.end_time
        )
    
    @classmethod
    def create_available_slot(
        cls,
        professional_id: str,
        schedule_date: date,
        start_time: time,
        end_time: time
    ) -> 'ProfessionalSchedule':
        """Create an available time slot."""
        return cls(
            professional_id=professional_id,
            schedule_date=schedule_date,
            start_time=start_time,
            end_time=end_time,
            schedule_type=ScheduleType.AVAILABLE
        )
    
    @classmethod
    def create_break(
        cls,
        professional_id: str,
        schedule_date: date,
        start_time: time,
        end_time: time,
        title: str = "Intervalo"
    ) -> 'ProfessionalSchedule':
        """Create a break time slot."""
        return cls(
            professional_id=professional_id,
            schedule_date=schedule_date,
            start_time=start_time,
            end_time=end_time,
            schedule_type=ScheduleType.BREAK,
            title=title
        )
    
    @classmethod
    def create_blocked_time(
        cls,
        professional_id: str,
        schedule_date: date,
        start_time: time,
        end_time: time,
        reason: str = None
    ) -> 'ProfessionalSchedule':
        """Create a blocked time slot."""
        return cls(
            professional_id=professional_id,
            schedule_date=schedule_date,
            start_time=start_time,
            end_time=end_time,
            schedule_type=ScheduleType.BLOCKED,
            description=reason
        )
    
    @classmethod
    def create_vacation(
        cls,
        professional_id: str,
        start_date: date,
        end_date: date,
        title: str = "Férias"
    ) -> List['ProfessionalSchedule']:
        """Create vacation time slots for date range."""
        schedules = []
        current_date = start_date
        
        while current_date <= end_date:
            schedule = cls(
                professional_id=professional_id,
                schedule_date=current_date,
                start_time=time(0, 0),  # All day
                end_time=time(23, 59),
                schedule_type=ScheduleType.VACATION,
                title=title
            )
            schedules.append(schedule)
            current_date += timedelta(days=1)
        
        return schedules
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'id': str(self.id),
            'professional_id': str(self.professional_id),
            'schedule_date': self.schedule_date.isoformat(),
            'start_time': self.start_time.strftime('%H:%M'),
            'end_time': self.end_time.strftime('%H:%M'),
            'schedule_type': self.schedule_type.value,
            'duration_minutes': self.duration_minutes,
            'title': self.title,
            'description': self.description,
            'is_recurring': self.is_recurring,
            'is_override': self.is_override,
            'is_available_slot': self.is_available_slot,
            'is_blocked_slot': self.is_blocked_slot,
            'is_past': self.is_past,
            'is_today': self.is_today,
            'is_future': self.is_future
        }
