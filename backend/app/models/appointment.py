"""
Appointment model for booking management.
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Numeric, JSON, ForeignKey, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum
from decimal import Decimal
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

from app.core.database import Base


class AppointmentStatus(str, enum.Enum):
    """Appointment status enum."""
    SCHEDULED = "scheduled"
    CONFIRMED = "confirmed"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"


class Appointment(Base):
    """Appointment model."""
    
    __tablename__ = "appointments"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign keys
    client_id = Column(UUID(as_uuid=True), Foreign<PERSON><PERSON>("users.id"), nullable=False, index=True)
    professional_id = Column(UUID(as_uuid=True), ForeignKey("professionals.id"), nullable=False, index=True)
    service_id = Column(UUID(as_uuid=True), ForeignKey("services.id"), nullable=False, index=True)
    
    # Appointment details
    appointment_date = Column(DateTime, nullable=False, index=True)
    duration_minutes = Column(Integer, nullable=False)
    status = Column(Enum(AppointmentStatus), default=AppointmentStatus.SCHEDULED, nullable=False, index=True)
    
    # Pricing
    service_price = Column(Numeric(10, 2), nullable=False)
    discount_amount = Column(Numeric(10, 2), default=0, nullable=False)
    total_price = Column(Numeric(10, 2), nullable=False)
    
    # Notes and instructions
    client_notes = Column(Text, nullable=True)  # Notes from client
    professional_notes = Column(Text, nullable=True)  # Notes from professional
    internal_notes = Column(Text, nullable=True)  # Internal salon notes
    
    # Confirmation
    is_confirmed = Column(Boolean, default=False, nullable=False)
    confirmed_at = Column(DateTime, nullable=True)
    confirmation_method = Column(String(50), nullable=True)  # email, sms, whatsapp, phone
    
    # Reminders
    reminder_sent_24h = Column(Boolean, default=False, nullable=False)
    reminder_sent_2h = Column(Boolean, default=False, nullable=False)
    reminder_sent_at = Column(DateTime, nullable=True)
    
    # Cancellation
    cancelled_at = Column(DateTime, nullable=True)
    cancelled_by = Column(String(50), nullable=True)  # client, professional, admin
    cancellation_reason = Column(Text, nullable=True)
    cancellation_fee = Column(Numeric(10, 2), default=0, nullable=False)
    
    # Completion
    completed_at = Column(DateTime, nullable=True)
    actual_duration_minutes = Column(Integer, nullable=True)
    
    # Rating and feedback
    client_rating = Column(Integer, nullable=True)  # 1-5 stars
    client_feedback = Column(Text, nullable=True)
    professional_rating = Column(Integer, nullable=True)  # Professional rates the client
    
    # Payment
    payment_status = Column(String(50), default="pending", nullable=False)
    payment_method = Column(String(50), nullable=True)
    paid_amount = Column(Numeric(10, 2), default=0, nullable=False)
    
    # Rescheduling
    original_appointment_id = Column(UUID(as_uuid=True), ForeignKey("appointments.id"), nullable=True)
    rescheduled_from_date = Column(DateTime, nullable=True)
    reschedule_count = Column(Integer, default=0, nullable=False)
    
    # Google Calendar integration
    google_calendar_event_id = Column(String(255), nullable=True)
    
    # Additional data
    extra_data = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime, nullable=True)  # Soft delete
    
    # Relationships
    client = relationship("User", foreign_keys=[client_id], back_populates="appointments_as_client")
    professional = relationship("Professional", back_populates="appointments")
    service = relationship("Service", back_populates="appointments")
    
    # Self-referential relationship for rescheduling
    original_appointment = relationship("Appointment", remote_side=[id], backref="rescheduled_appointments")
    
    # Payment relationship
    payments = relationship("Payment", back_populates="appointment", cascade="all, delete-orphan")
    
    # Notification relationship
    notifications = relationship("Notification", back_populates="appointment", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Appointment(id={self.id}, date={self.appointment_date}, status={self.status})>"
    
    @property
    def end_time(self) -> datetime:
        """Calculate appointment end time."""
        return self.appointment_date + timedelta(minutes=self.duration_minutes)
    
    @property
    def is_past(self) -> bool:
        """Check if appointment is in the past."""
        return self.appointment_date < datetime.utcnow()
    
    @property
    def is_today(self) -> bool:
        """Check if appointment is today."""
        today = datetime.utcnow().date()
        return self.appointment_date.date() == today
    
    @property
    def is_upcoming(self) -> bool:
        """Check if appointment is upcoming."""
        return self.appointment_date > datetime.utcnow()
    
    @property
    def can_be_cancelled(self) -> bool:
        """Check if appointment can be cancelled."""
        if self.status in [AppointmentStatus.CANCELLED, AppointmentStatus.COMPLETED, AppointmentStatus.NO_SHOW]:
            return False
        
        # Check cancellation policy (e.g., 24 hours before)
        cancellation_deadline = self.appointment_date - timedelta(hours=24)
        return datetime.utcnow() < cancellation_deadline
    
    @property
    def can_be_rescheduled(self) -> bool:
        """Check if appointment can be rescheduled."""
        return self.can_be_cancelled and self.reschedule_count < 3  # Max 3 reschedules
    
    @property
    def time_until_appointment(self) -> timedelta:
        """Get time until appointment."""
        return self.appointment_date - datetime.utcnow()
    
    @property
    def hours_until_appointment(self) -> float:
        """Get hours until appointment."""
        delta = self.time_until_appointment
        return delta.total_seconds() / 3600
    
    @property
    def needs_reminder(self) -> bool:
        """Check if appointment needs reminder."""
        if self.status != AppointmentStatus.SCHEDULED:
            return False
        
        hours_until = self.hours_until_appointment
        
        # 24-hour reminder
        if 23 <= hours_until <= 25 and not self.reminder_sent_24h:
            return True
        
        # 2-hour reminder
        if 1.5 <= hours_until <= 2.5 and not self.reminder_sent_2h:
            return True
        
        return False
    
    @property
    def is_paid(self) -> bool:
        """Check if appointment is fully paid."""
        return self.paid_amount >= self.total_price
    
    @property
    def remaining_balance(self) -> Decimal:
        """Get remaining balance to be paid."""
        return max(Decimal('0.00'), self.total_price - self.paid_amount)
    
    def calculate_total_price(self):
        """Calculate total price with discounts."""
        self.total_price = self.service_price - self.discount_amount
    
    def confirm(self, method: str = "system"):
        """Confirm the appointment."""
        self.is_confirmed = True
        self.confirmed_at = datetime.utcnow()
        self.confirmation_method = method
        if self.status == AppointmentStatus.SCHEDULED:
            self.status = AppointmentStatus.CONFIRMED
    
    def cancel(self, cancelled_by: str, reason: str = None, fee: Decimal = None):
        """Cancel the appointment."""
        self.status = AppointmentStatus.CANCELLED
        self.cancelled_at = datetime.utcnow()
        self.cancelled_by = cancelled_by
        self.cancellation_reason = reason
        if fee:
            self.cancellation_fee = fee
    
    def complete(self, actual_duration: int = None):
        """Mark appointment as completed."""
        self.status = AppointmentStatus.COMPLETED
        self.completed_at = datetime.utcnow()
        if actual_duration:
            self.actual_duration_minutes = actual_duration
    
    def mark_no_show(self):
        """Mark appointment as no-show."""
        self.status = AppointmentStatus.NO_SHOW
    
    def start_service(self):
        """Start the service (mark as in progress)."""
        self.status = AppointmentStatus.IN_PROGRESS
    
    def reschedule(self, new_date: datetime) -> 'Appointment':
        """Reschedule appointment to new date."""
        # Create new appointment
        new_appointment = Appointment(
            client_id=self.client_id,
            professional_id=self.professional_id,
            service_id=self.service_id,
            appointment_date=new_date,
            duration_minutes=self.duration_minutes,
            service_price=self.service_price,
            discount_amount=self.discount_amount,
            total_price=self.total_price,
            client_notes=self.client_notes,
            original_appointment_id=self.id,
            rescheduled_from_date=self.appointment_date,
            reschedule_count=self.reschedule_count + 1
        )
        
        # Cancel current appointment
        self.cancel("client", "Reagendado")
        
        return new_appointment
    
    def add_rating(self, rating: int, feedback: str = None):
        """Add client rating and feedback."""
        if 1 <= rating <= 5:
            self.client_rating = rating
            self.client_feedback = feedback
    
    def mark_reminder_sent(self, reminder_type: str):
        """Mark reminder as sent."""
        if reminder_type == "24h":
            self.reminder_sent_24h = True
        elif reminder_type == "2h":
            self.reminder_sent_2h = True
        self.reminder_sent_at = datetime.utcnow()
    
    def soft_delete(self):
        """Soft delete appointment."""
        self.deleted_at = datetime.utcnow()
    
    def restore(self):
        """Restore soft deleted appointment."""
        self.deleted_at = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert appointment to dictionary."""
        return {
            'id': str(self.id),
            'client_id': str(self.client_id),
            'professional_id': str(self.professional_id),
            'service_id': str(self.service_id),
            'appointment_date': self.appointment_date.isoformat(),
            'end_time': self.end_time.isoformat(),
            'duration_minutes': self.duration_minutes,
            'status': self.status.value,
            'service_price': float(self.service_price),
            'total_price': float(self.total_price),
            'is_confirmed': self.is_confirmed,
            'is_paid': self.is_paid,
            'remaining_balance': float(self.remaining_balance),
            'client_notes': self.client_notes,
            'professional_notes': self.professional_notes,
            'client_rating': self.client_rating,
            'client_feedback': self.client_feedback,
            'created_at': self.created_at.isoformat()
        }
