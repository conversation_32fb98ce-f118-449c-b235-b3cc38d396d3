"""
Notification model for managing communications.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Text, JSON, Foreign<PERSON>ey, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum
from typing import Dict, Any, Optional
from datetime import datetime

from app.core.database import Base


class NotificationType(str, enum.Enum):
    """Notification type enum."""
    EMAIL = "email"
    SMS = "sms"
    WHATSAPP = "whatsapp"
    PUSH = "push"


class NotificationStatus(str, enum.Enum):
    """Notification status enum."""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    READ = "read"


class NotificationCategory(str, enum.Enum):
    """Notification category enum."""
    APPOINTMENT_REMINDER = "appointment_reminder"
    APPOINTMENT_CONFIRMATION = "appointment_confirmation"
    APPOINTMENT_CANCELLED = "appointment_cancelled"
    APPOINTMENT_RESCHEDULED = "appointment_rescheduled"
    PAYMENT_CONFIRMATION = "payment_confirmation"
    PAYMENT_FAILED = "payment_failed"
    WELCOME = "welcome"
    PASSWORD_RESET = "password_reset"
    EMAIL_VERIFICATION = "email_verification"
    PROMOTIONAL = "promotional"
    SYSTEM_ALERT = "system_alert"


class Notification(Base):
    """Notification model."""
    
    __tablename__ = "notifications"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign keys
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    appointment_id = Column(UUID(as_uuid=True), ForeignKey("appointments.id"), nullable=True, index=True)
    
    # Notification details
    type = Column(Enum(NotificationType), nullable=False, index=True)
    category = Column(Enum(NotificationCategory), nullable=False, index=True)
    status = Column(Enum(NotificationStatus), default=NotificationStatus.PENDING, nullable=False, index=True)
    
    # Content
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    html_content = Column(Text, nullable=True)  # For email notifications
    
    # Recipient details
    recipient_email = Column(String(255), nullable=True)
    recipient_phone = Column(String(20), nullable=True)
    recipient_name = Column(String(255), nullable=True)
    
    # Delivery details
    sent_at = Column(DateTime, nullable=True)
    delivered_at = Column(DateTime, nullable=True)
    read_at = Column(DateTime, nullable=True)
    failed_at = Column(DateTime, nullable=True)
    
    # Provider details
    provider = Column(String(50), nullable=True)  # sendgrid, twilio, whatsapp, etc.
    provider_message_id = Column(String(255), nullable=True)
    provider_response = Column(JSON, nullable=True)
    
    # Scheduling
    scheduled_for = Column(DateTime, nullable=True)  # For scheduled notifications
    retry_count = Column(Integer, default=0, nullable=False)
    max_retries = Column(Integer, default=3, nullable=False)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    error_code = Column(String(50), nullable=True)
    
    # Template and personalization
    template_name = Column(String(100), nullable=True)
    template_variables = Column(JSON, nullable=True)
    
    # Tracking
    is_read = Column(Boolean, default=False, nullable=False)
    read_count = Column(Integer, default=0, nullable=False)
    
    # Additional metadata
    metadata = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="notifications")
    appointment = relationship("Appointment", back_populates="notifications")
    
    def __repr__(self):
        return f"<Notification(id={self.id}, type={self.type}, status={self.status})>"
    
    @property
    def is_pending(self) -> bool:
        """Check if notification is pending."""
        return self.status == NotificationStatus.PENDING
    
    @property
    def is_sent(self) -> bool:
        """Check if notification is sent."""
        return self.status in [NotificationStatus.SENT, NotificationStatus.DELIVERED, NotificationStatus.READ]
    
    @property
    def is_failed(self) -> bool:
        """Check if notification failed."""
        return self.status == NotificationStatus.FAILED
    
    @property
    def can_retry(self) -> bool:
        """Check if notification can be retried."""
        return self.is_failed and self.retry_count < self.max_retries
    
    @property
    def is_scheduled(self) -> bool:
        """Check if notification is scheduled for future."""
        if not self.scheduled_for:
            return False
        return self.scheduled_for > datetime.utcnow()
    
    @property
    def is_due(self) -> bool:
        """Check if scheduled notification is due."""
        if not self.scheduled_for:
            return True  # Not scheduled, can be sent immediately
        return self.scheduled_for <= datetime.utcnow()
    
    def mark_sent(self, provider_message_id: str = None, provider_response: Dict = None):
        """Mark notification as sent."""
        self.status = NotificationStatus.SENT
        self.sent_at = datetime.utcnow()
        
        if provider_message_id:
            self.provider_message_id = provider_message_id
        
        if provider_response:
            self.provider_response = provider_response
    
    def mark_delivered(self):
        """Mark notification as delivered."""
        self.status = NotificationStatus.DELIVERED
        self.delivered_at = datetime.utcnow()
    
    def mark_read(self):
        """Mark notification as read."""
        if not self.is_read:
            self.is_read = True
            self.read_at = datetime.utcnow()
            self.status = NotificationStatus.READ
        
        self.read_count += 1
    
    def mark_failed(self, error_message: str, error_code: str = None):
        """Mark notification as failed."""
        self.status = NotificationStatus.FAILED
        self.failed_at = datetime.utcnow()
        self.error_message = error_message
        
        if error_code:
            self.error_code = error_code
    
    def increment_retry(self):
        """Increment retry count."""
        self.retry_count += 1
        
        if self.retry_count < self.max_retries:
            self.status = NotificationStatus.PENDING
        else:
            self.mark_failed("Max retries exceeded")
    
    def schedule_for(self, scheduled_time: datetime):
        """Schedule notification for specific time."""
        self.scheduled_for = scheduled_time
    
    def set_template_variables(self, variables: Dict[str, Any]):
        """Set template variables for personalization."""
        self.template_variables = variables
    
    def add_metadata(self, key: str, value: Any):
        """Add metadata."""
        if not self.metadata:
            self.metadata = {}
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata value."""
        if not self.metadata:
            return default
        return self.metadata.get(key, default)
    
    def get_personalized_content(self) -> Dict[str, str]:
        """Get personalized content with template variables applied."""
        content = {
            'title': self.title,
            'message': self.message,
            'html_content': self.html_content
        }
        
        if self.template_variables:
            for key, value in content.items():
                if value:
                    for var_name, var_value in self.template_variables.items():
                        placeholder = f"{{{{{var_name}}}}}"
                        content[key] = value.replace(placeholder, str(var_value))
        
        return content
    
    @classmethod
    def create_appointment_reminder(
        cls,
        user_id: str,
        appointment_id: str,
        notification_type: NotificationType,
        scheduled_for: datetime = None
    ) -> 'Notification':
        """Create appointment reminder notification."""
        return cls(
            user_id=user_id,
            appointment_id=appointment_id,
            type=notification_type,
            category=NotificationCategory.APPOINTMENT_REMINDER,
            title="Lembrete de Agendamento",
            message="Você tem um agendamento em breve.",
            scheduled_for=scheduled_for
        )
    
    @classmethod
    def create_appointment_confirmation(
        cls,
        user_id: str,
        appointment_id: str,
        notification_type: NotificationType
    ) -> 'Notification':
        """Create appointment confirmation notification."""
        return cls(
            user_id=user_id,
            appointment_id=appointment_id,
            type=notification_type,
            category=NotificationCategory.APPOINTMENT_CONFIRMATION,
            title="Agendamento Confirmado",
            message="Seu agendamento foi confirmado com sucesso."
        )
    
    @classmethod
    def create_payment_confirmation(
        cls,
        user_id: str,
        notification_type: NotificationType,
        amount: str
    ) -> 'Notification':
        """Create payment confirmation notification."""
        return cls(
            user_id=user_id,
            type=notification_type,
            category=NotificationCategory.PAYMENT_CONFIRMATION,
            title="Pagamento Confirmado",
            message=f"Seu pagamento de {amount} foi processado com sucesso."
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert notification to dictionary."""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'appointment_id': str(self.appointment_id) if self.appointment_id else None,
            'type': self.type.value,
            'category': self.category.value,
            'status': self.status.value,
            'title': self.title,
            'message': self.message,
            'is_read': self.is_read,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'scheduled_for': self.scheduled_for.isoformat() if self.scheduled_for else None,
            'created_at': self.created_at.isoformat()
        }
