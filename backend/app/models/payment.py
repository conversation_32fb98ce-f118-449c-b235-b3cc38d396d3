"""
Payment model for transaction management.
"""

from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, String, Boolean, DateTime, Text, Numeric, JSON, ForeignKey, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum
from decimal import Decimal
from typing import Dict, Any, Optional
from datetime import datetime

from app.core.database import Base


class PaymentStatus(str, enum.Enum):
    """Payment status enum."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    REFUNDED = "refunded"
    CANCELLED = "cancelled"


class PaymentMethod(str, enum.Enum):
    """Payment method enum."""
    CREDIT_CARD = "credit_card"
    DEBIT_CARD = "debit_card"
    PIX = "pix"
    CASH = "cash"
    MERCADO_PAGO = "mercado_pago"
    STRIPE = "stripe"


class Payment(Base):
    """Payment model."""
    
    __tablename__ = "payments"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign keys
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    appointment_id = Column(UUID(as_uuid=True), ForeignKey("appointments.id"), nullable=True, index=True)
    
    # Payment details
    amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), default="BRL", nullable=False)
    status = Column(Enum(PaymentStatus), default=PaymentStatus.PENDING, nullable=False, index=True)
    method = Column(Enum(PaymentMethod), nullable=False)
    
    # External payment provider details
    provider = Column(String(50), nullable=True)  # mercadopago, stripe, etc.
    provider_payment_id = Column(String(255), nullable=True, index=True)
    provider_transaction_id = Column(String(255), nullable=True)
    
    # Payment metadata
    description = Column(Text, nullable=True)
    reference_id = Column(String(255), nullable=True, index=True)  # Internal reference
    
    # Card details (if applicable)
    card_last_four = Column(String(4), nullable=True)
    card_brand = Column(String(50), nullable=True)
    
    # PIX details (if applicable)
    pix_key = Column(String(255), nullable=True)
    pix_qr_code = Column(Text, nullable=True)
    pix_copy_paste = Column(Text, nullable=True)
    
    # Processing details
    processed_at = Column(DateTime, nullable=True)
    processing_fee = Column(Numeric(10, 2), default=0, nullable=False)
    net_amount = Column(Numeric(10, 2), nullable=True)  # Amount after fees
    
    # Refund details
    refunded_at = Column(DateTime, nullable=True)
    refund_amount = Column(Numeric(10, 2), default=0, nullable=False)
    refund_reason = Column(Text, nullable=True)
    refund_reference = Column(String(255), nullable=True)
    
    # Failure details
    failed_at = Column(DateTime, nullable=True)
    failure_reason = Column(Text, nullable=True)
    failure_code = Column(String(50), nullable=True)
    
    # Webhook and callback data
    webhook_data = Column(JSON, nullable=True)
    callback_url = Column(String(500), nullable=True)
    
    # Additional metadata
    metadata = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    expires_at = Column(DateTime, nullable=True)  # Payment expiration
    
    # Relationships
    user = relationship("User", back_populates="payments")
    appointment = relationship("Appointment", back_populates="payments")
    
    def __repr__(self):
        return f"<Payment(id={self.id}, amount={self.amount}, status={self.status})>"
    
    @property
    def is_completed(self) -> bool:
        """Check if payment is completed."""
        return self.status == PaymentStatus.COMPLETED
    
    @property
    def is_pending(self) -> bool:
        """Check if payment is pending."""
        return self.status == PaymentStatus.PENDING
    
    @property
    def is_failed(self) -> bool:
        """Check if payment failed."""
        return self.status == PaymentStatus.FAILED
    
    @property
    def is_refunded(self) -> bool:
        """Check if payment is refunded."""
        return self.status == PaymentStatus.REFUNDED
    
    @property
    def can_be_refunded(self) -> bool:
        """Check if payment can be refunded."""
        return self.status == PaymentStatus.COMPLETED and self.refund_amount == 0
    
    @property
    def is_expired(self) -> bool:
        """Check if payment is expired."""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
    
    @property
    def refundable_amount(self) -> Decimal:
        """Get amount that can be refunded."""
        return self.amount - self.refund_amount
    
    @property
    def formatted_amount(self) -> str:
        """Get formatted amount with currency."""
        return f"R$ {self.amount:.2f}"
    
    @property
    def masked_card_number(self) -> Optional[str]:
        """Get masked card number."""
        if self.card_last_four:
            return f"**** **** **** {self.card_last_four}"
        return None
    
    def complete(self, provider_transaction_id: str = None, processing_fee: Decimal = None):
        """Mark payment as completed."""
        self.status = PaymentStatus.COMPLETED
        self.processed_at = datetime.utcnow()
        
        if provider_transaction_id:
            self.provider_transaction_id = provider_transaction_id
        
        if processing_fee:
            self.processing_fee = processing_fee
            self.net_amount = self.amount - processing_fee
        else:
            self.net_amount = self.amount
    
    def fail(self, reason: str, code: str = None):
        """Mark payment as failed."""
        self.status = PaymentStatus.FAILED
        self.failed_at = datetime.utcnow()
        self.failure_reason = reason
        if code:
            self.failure_code = code
    
    def cancel(self):
        """Cancel payment."""
        self.status = PaymentStatus.CANCELLED
    
    def refund(self, amount: Decimal = None, reason: str = None) -> Decimal:
        """Process refund."""
        if not self.can_be_refunded:
            raise ValueError("Payment cannot be refunded")
        
        refund_amount = amount or self.refundable_amount
        
        if refund_amount > self.refundable_amount:
            raise ValueError("Refund amount exceeds refundable amount")
        
        self.refund_amount += refund_amount
        self.refund_reason = reason
        self.refunded_at = datetime.utcnow()
        
        # If fully refunded, update status
        if self.refund_amount >= self.amount:
            self.status = PaymentStatus.REFUNDED
        
        return refund_amount
    
    def set_processing(self):
        """Set payment as processing."""
        self.status = PaymentStatus.PROCESSING
    
    def update_from_webhook(self, webhook_data: Dict[str, Any]):
        """Update payment from webhook data."""
        self.webhook_data = webhook_data
        
        # Update status based on webhook data
        # This would be implemented based on specific payment provider
        pass
    
    def generate_reference_id(self) -> str:
        """Generate internal reference ID."""
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        short_id = str(self.id)[:8]
        self.reference_id = f"PAY-{timestamp}-{short_id}"
        return self.reference_id
    
    def set_card_details(self, last_four: str, brand: str):
        """Set card details."""
        self.card_last_four = last_four
        self.card_brand = brand
    
    def set_pix_details(self, qr_code: str, copy_paste: str, key: str = None):
        """Set PIX payment details."""
        self.pix_qr_code = qr_code
        self.pix_copy_paste = copy_paste
        if key:
            self.pix_key = key
    
    def set_expiration(self, minutes: int = 30):
        """Set payment expiration."""
        from datetime import timedelta
        self.expires_at = datetime.utcnow() + timedelta(minutes=minutes)
    
    def add_metadata(self, key: str, value: Any):
        """Add metadata."""
        if not self.metadata:
            self.metadata = {}
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata value."""
        if not self.metadata:
            return default
        return self.metadata.get(key, default)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert payment to dictionary."""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'appointment_id': str(self.appointment_id) if self.appointment_id else None,
            'amount': float(self.amount),
            'currency': self.currency,
            'status': self.status.value,
            'method': self.method.value,
            'provider': self.provider,
            'provider_payment_id': self.provider_payment_id,
            'description': self.description,
            'reference_id': self.reference_id,
            'formatted_amount': self.formatted_amount,
            'masked_card_number': self.masked_card_number,
            'is_completed': self.is_completed,
            'is_pending': self.is_pending,
            'is_failed': self.is_failed,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'created_at': self.created_at.isoformat(),
            'expires_at': self.expires_at.isoformat() if self.expires_at else None
        }
