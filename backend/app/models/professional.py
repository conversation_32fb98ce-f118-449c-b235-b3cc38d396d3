"""
Professional model for salon professionals.
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Numeric, JSON, ForeignKey, Time
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from decimal import Decimal
from typing import List, Dict, Any, Optional
from datetime import datetime, time

from app.core.database import Base


class Professional(Base):
    """Professional model."""
    
    __tablename__ = "professionals"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign key to user
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, unique=True)
    
    # Professional information
    license_number = Column(String(100), nullable=True, unique=True)
    specialties = Column(ARRAY(String), nullable=False)  # e.g., ['Cabelo', 'Unhas', 'Estética']
    experience_years = Column(Integer, nullable=True)
    
    # Profile
    bio = Column(Text, nullable=True)
    portfolio_urls = Column(ARRAY(String), nullable=True)  # Portfolio images
    certifications = Column(ARRAY(String), nullable=True)
    
    # Availability
    is_available = Column(Boolean, default=True, nullable=False)
    is_accepting_new_clients = Column(Boolean, default=True, nullable=False)
    
    # Working hours (JSON format for flexibility)
    working_hours = Column(JSON, nullable=True)  # e.g., {"monday": {"start": "09:00", "end": "17:00"}}
    
    # Pricing
    base_hourly_rate = Column(Numeric(10, 2), nullable=True)
    commission_percentage = Column(Numeric(5, 2), default=50.00, nullable=False)
    
    # Performance metrics
    total_appointments = Column(Integer, default=0, nullable=False)
    completed_appointments = Column(Integer, default=0, nullable=False)
    cancelled_appointments = Column(Integer, default=0, nullable=False)
    no_show_appointments = Column(Integer, default=0, nullable=False)
    
    # Ratings
    rating_average = Column(Numeric(3, 2), nullable=True)
    rating_count = Column(Integer, default=0, nullable=False)
    
    # Social media
    instagram_handle = Column(String(100), nullable=True)
    facebook_url = Column(String(500), nullable=True)
    website_url = Column(String(500), nullable=True)
    
    # Employment details
    hire_date = Column(DateTime, nullable=True)
    employment_type = Column(String(50), default="employee", nullable=False)  # employee, contractor, owner
    
    # Preferences
    preferred_break_duration = Column(Integer, default=30, nullable=False)  # minutes
    max_daily_appointments = Column(Integer, nullable=True)
    advance_booking_limit_days = Column(Integer, default=30, nullable=False)
    
    # Additional data
    extra_data = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime, nullable=True)  # Soft delete
    
    # Relationships
    user = relationship("User", back_populates="professional_profile")
    
    appointments = relationship(
        "Appointment",
        foreign_keys="Appointment.professional_id",
        back_populates="professional",
        cascade="all, delete-orphan"
    )
    
    professional_services = relationship(
        "ProfessionalService",
        back_populates="professional",
        cascade="all, delete-orphan"
    )

    schedules = relationship(
        "ProfessionalSchedule",
        back_populates="professional",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self):
        return f"<Professional(id={self.id}, user_id={self.user_id})>"
    
    @property
    def full_name(self) -> str:
        """Get professional's full name from user."""
        return self.user.full_name if self.user else ""
    
    @property
    def email(self) -> str:
        """Get professional's email from user."""
        return self.user.email if self.user else ""
    
    @property
    def phone(self) -> str:
        """Get professional's phone from user."""
        return self.user.phone if self.user else ""
    
    @property
    def avatar_url(self) -> str:
        """Get professional's avatar from user."""
        return self.user.avatar_url if self.user else ""
    
    @property
    def completion_rate(self) -> float:
        """Calculate appointment completion rate."""
        if self.total_appointments == 0:
            return 0.0
        return (self.completed_appointments / self.total_appointments) * 100
    
    @property
    def cancellation_rate(self) -> float:
        """Calculate appointment cancellation rate."""
        if self.total_appointments == 0:
            return 0.0
        return (self.cancelled_appointments / self.total_appointments) * 100
    
    @property
    def no_show_rate(self) -> float:
        """Calculate no-show rate."""
        if self.total_appointments == 0:
            return 0.0
        return (self.no_show_appointments / self.total_appointments) * 100
    
    @property
    def average_rating_stars(self) -> int:
        """Get average rating as stars (1-5)."""
        if not self.rating_average:
            return 0
        return round(float(self.rating_average))
    
    @property
    def is_active(self) -> bool:
        """Check if professional is active."""
        return self.is_available and not self.deleted_at and self.user.is_active
    
    def has_specialty(self, specialty: str) -> bool:
        """Check if professional has specific specialty."""
        return specialty in (self.specialties or [])
    
    def can_perform_service(self, service_skills: List[str]) -> bool:
        """Check if professional can perform service based on required skills."""
        if not service_skills:
            return True
        return any(skill in (self.specialties or []) for skill in service_skills)
    
    def get_working_hours(self, day_of_week: int) -> Optional[Dict[str, str]]:
        """Get working hours for specific day (0=Monday, 6=Sunday)."""
        if not self.working_hours:
            return None
        
        day_names = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
        day_name = day_names[day_of_week]
        
        return self.working_hours.get(day_name)
    
    def is_working_on_day(self, day_of_week: int) -> bool:
        """Check if professional works on specific day."""
        working_hours = self.get_working_hours(day_of_week)
        return working_hours is not None and working_hours.get("start") and working_hours.get("end")
    
    def is_working_at_time(self, day_of_week: int, check_time: time) -> bool:
        """Check if professional is working at specific time."""
        working_hours = self.get_working_hours(day_of_week)
        if not working_hours:
            return False
        
        try:
            start_time = datetime.strptime(working_hours["start"], "%H:%M").time()
            end_time = datetime.strptime(working_hours["end"], "%H:%M").time()
            return start_time <= check_time <= end_time
        except (ValueError, KeyError):
            return False
    
    def update_rating(self, new_rating: float):
        """Update professional rating."""
        if self.rating_count == 0:
            self.rating_average = Decimal(str(new_rating))
            self.rating_count = 1
        else:
            current_total = float(self.rating_average) * self.rating_count
            new_total = current_total + new_rating
            self.rating_count += 1
            self.rating_average = Decimal(str(new_total / self.rating_count))
    
    def increment_appointment_count(self, status: str):
        """Increment appointment counters based on status."""
        self.total_appointments += 1
        
        if status == "completed":
            self.completed_appointments += 1
        elif status == "cancelled":
            self.cancelled_appointments += 1
        elif status == "no_show":
            self.no_show_appointments += 1
    
    def set_default_working_hours(self):
        """Set default working hours (Monday to Saturday, 9 AM to 6 PM)."""
        self.working_hours = {
            "monday": {"start": "09:00", "end": "18:00"},
            "tuesday": {"start": "09:00", "end": "18:00"},
            "wednesday": {"start": "09:00", "end": "18:00"},
            "thursday": {"start": "09:00", "end": "18:00"},
            "friday": {"start": "09:00", "end": "18:00"},
            "saturday": {"start": "09:00", "end": "18:00"},
            "sunday": None  # Not working on Sunday
        }
    
    def soft_delete(self):
        """Soft delete professional."""
        from datetime import datetime
        self.deleted_at = datetime.utcnow()
        self.is_available = False
    
    def restore(self):
        """Restore soft deleted professional."""
        self.deleted_at = None
        self.is_available = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert professional to dictionary."""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'full_name': self.full_name,
            'email': self.email,
            'phone': self.phone,
            'avatar_url': self.avatar_url,
            'specialties': self.specialties,
            'experience_years': self.experience_years,
            'bio': self.bio,
            'is_available': self.is_available,
            'is_accepting_new_clients': self.is_accepting_new_clients,
            'rating_average': float(self.rating_average) if self.rating_average else None,
            'rating_count': self.rating_count,
            'completion_rate': self.completion_rate,
            'working_hours': self.working_hours,
            'instagram_handle': self.instagram_handle
        }
