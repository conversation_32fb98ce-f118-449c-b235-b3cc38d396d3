"""
Professional Service model for many-to-many relationship between professionals and services.
"""

from sqlalchemy import <PERSON>umn, Integer, Boolean, DateTime, Numeric, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from decimal import Decimal
from typing import Dict, Any

from app.core.database import Base


class ProfessionalService(Base):
    """Professional Service relationship model."""
    
    __tablename__ = "professional_services"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign keys
    professional_id = Column(UUID(as_uuid=True), ForeignKey("professionals.id"), nullable=False, index=True)
    service_id = Column(UUID(as_uuid=True), ForeignKey("services.id"), nullable=False, index=True)
    
    # Professional-specific pricing (optional override)
    custom_price = Column(Numeric(10, 2), nullable=True)
    custom_duration_minutes = Column(Integer, nullable=True)
    
    # Availability
    is_available = Column(Boolean, default=True, nullable=False)
    
    # Performance metrics for this professional-service combination
    total_bookings = Column(Integer, default=0, nullable=False)
    average_rating = Column(Numeric(3, 2), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    professional = relationship("Professional", back_populates="professional_services")
    service = relationship("Service", back_populates="professional_services")
    
    # Unique constraint to prevent duplicate relationships
    __table_args__ = (
        UniqueConstraint('professional_id', 'service_id', name='unique_professional_service'),
    )
    
    def __repr__(self):
        return f"<ProfessionalService(professional_id={self.professional_id}, service_id={self.service_id})>"
    
    @property
    def effective_price(self) -> Decimal:
        """Get effective price (custom or service default)."""
        return self.custom_price or self.service.current_price
    
    @property
    def effective_duration(self) -> int:
        """Get effective duration (custom or service default)."""
        return self.custom_duration_minutes or self.service.duration_minutes
    
    def increment_booking_count(self):
        """Increment booking count."""
        self.total_bookings += 1
    
    def update_rating(self, new_rating: float):
        """Update average rating."""
        if not self.average_rating:
            self.average_rating = Decimal(str(new_rating))
        else:
            # Simple moving average (could be improved with rating count)
            current_avg = float(self.average_rating)
            self.average_rating = Decimal(str((current_avg + new_rating) / 2))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'id': str(self.id),
            'professional_id': str(self.professional_id),
            'service_id': str(self.service_id),
            'custom_price': float(self.custom_price) if self.custom_price else None,
            'custom_duration_minutes': self.custom_duration_minutes,
            'effective_price': float(self.effective_price),
            'effective_duration': self.effective_duration,
            'is_available': self.is_available,
            'total_bookings': self.total_bookings,
            'average_rating': float(self.average_rating) if self.average_rating else None
        }
