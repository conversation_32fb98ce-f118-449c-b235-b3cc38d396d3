# FastAPI and ASGI
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Redis
redis==5.0.1
aioredis==2.0.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2

# Validation & Serialization
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# Date & Time
python-dateutil==2.8.2
pytz==2023.3

# File Upload & Processing
python-magic==0.4.27
Pillow==10.1.0

# Email
sendgrid==6.10.0

# SMS
twilio==8.10.0

# Payment Gateways
mercadopago==2.2.1
stripe==7.8.0

# Google APIs
google-auth==2.25.2
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.1.1
google-api-python-client==2.108.0

# WhatsApp
requests==2.31.0

# Utilities
python-dotenv==1.0.0
click==8.1.7
rich==13.7.0

# Logging
loguru==0.7.2

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
faker==20.1.0

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Monitoring
prometheus-client==0.19.0

# Task Queue (Optional)
celery==5.3.4
flower==2.0.1

# Rate Limiting
slowapi==0.1.9

# CORS
fastapi-cors==0.0.6

# Pagination
fastapi-pagination==0.12.13

# Background Tasks
apscheduler==3.10.4

# File Storage (Optional)
boto3==1.34.0  # For AWS S3
azure-storage-blob==12.19.0  # For Azure Blob

# Caching
fastapi-cache2==0.2.1

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8
