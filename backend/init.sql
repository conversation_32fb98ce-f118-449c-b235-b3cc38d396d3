-- Initialize database with extensions and basic setup

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable PostGIS if needed for location features (optional)
-- CREATE EXTENSION IF NOT EXISTS postgis;

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('client', 'professional', 'admin', 'owner');
CREATE TYPE appointment_status AS ENUM ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show');
CREATE TYPE payment_status AS ENUM ('pending', 'processing', 'completed', 'failed', 'refunded', 'cancelled');
CREATE TYPE payment_method AS ENUM ('credit_card', 'debit_card', 'pix', 'cash', 'mercado_pago', 'stripe');
CREATE TYPE notification_type AS ENUM ('email', 'sms', 'whatsapp', 'push');
CREATE TYPE notification_status AS ENUM ('pending', 'sent', 'delivered', 'failed');

-- Create indexes for better performance
-- These will be created by Alembic migrations, but we can prepare the database

-- Set timezone
SET timezone = 'America/Sao_Paulo';

-- Create audit trigger function for tracking changes
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create function to generate short IDs
CREATE OR REPLACE FUNCTION generate_short_id(length INTEGER DEFAULT 8)
RETURNS TEXT AS $$
DECLARE
    chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    result TEXT := '';
    i INTEGER := 0;
BEGIN
    FOR i IN 1..length LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::INTEGER, 1);
    END LOOP;
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Create function to check business hours
CREATE OR REPLACE FUNCTION is_business_hours(check_time TIME, check_day INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
    start_time TIME := '09:00:00';
    end_time TIME := '18:00:00';
    business_days INTEGER[] := ARRAY[1,2,3,4,5,6]; -- Monday to Saturday
BEGIN
    RETURN check_day = ANY(business_days) AND check_time BETWEEN start_time AND end_time;
END;
$$ LANGUAGE plpgsql;

-- Create function to calculate appointment end time
CREATE OR REPLACE FUNCTION calculate_appointment_end_time(start_time TIMESTAMP, duration_minutes INTEGER)
RETURNS TIMESTAMP AS $$
BEGIN
    RETURN start_time + INTERVAL '1 minute' * duration_minutes;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE salon_db TO salon_user;
GRANT ALL ON SCHEMA public TO salon_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO salon_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO salon_user;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO salon_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO salon_user;
